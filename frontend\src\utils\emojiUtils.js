/**
 * Utility functions for handling emoji in labels
 */

// Danh sách emoji được hỗ trợ (giống với EmojiSelector)
const SUPPORTED_EMOJIS = [
    "🔥", "✅", "🐛", "🚩", "📌", "🎯", "⚡", "🔧", "💡", "📝",
    "🚀", "⭐", "🔒", "🌟", "📊", "🎨", "🔍", "💻", "📱", "🌐"
];

/**
 * Kiểm tra xem một ký tự có phải là emoji được hỗ trợ không
 * @param {string} char - Ký tự cần kiểm tra
 * @returns {boolean} - True nếu là emoji được hỗ trợ
 */
export const isSupportedEmoji = (char) => {
    return SUPPORTED_EMOJIS.includes(char);
};

/**
 * Tách emoji khỏi tên label
 * @param {string} labelName - Tên label có thể chứa emoji
 * @returns {object} - Object chứa emoji và name
 */
export const parseEmojiFromLabel = (labelName) => {
    if (!labelName || typeof labelName !== 'string') {
        return { emoji: '', name: '' };
    }

    const firstChar = labelName.charAt(0);
    
    // Kiểm tra xem ký tự đầu có phải là emoji được hỗ trợ không
    if (isSupportedEmoji(firstChar)) {
        // Kiểm tra xem có khoảng trắng sau emoji không
        if (labelName.length > 1 && labelName.charAt(1) === ' ') {
            return {
                emoji: firstChar,
                name: labelName.slice(2) // Bỏ emoji và khoảng trắng
            };
        } else if (labelName.length > 1) {
            // Trường hợp không có khoảng trắng sau emoji
            return {
                emoji: firstChar,
                name: labelName.slice(1)
            };
        } else {
            // Chỉ có emoji, không có tên
            return {
                emoji: firstChar,
                name: ''
            };
        }
    }

    // Không có emoji ở đầu
    return {
        emoji: '',
        name: labelName
    };
};

/**
 * Gộp emoji và tên thành label name hoàn chỉnh
 * @param {string} emoji - Emoji được chọn
 * @param {string} name - Tên label
 * @returns {string} - Label name hoàn chỉnh
 */
export const combineEmojiWithName = (emoji, name) => {
    if (!name || typeof name !== 'string') {
        return emoji || '';
    }

    const trimmedName = name.trim();
    
    if (!emoji || !isSupportedEmoji(emoji)) {
        return trimmedName;
    }

    if (!trimmedName) {
        return emoji;
    }

    return `${emoji} ${trimmedName}`;
};

/**
 * Validate label name (có thể chứa emoji)
 * @param {string} labelName - Tên label cần validate
 * @returns {object} - Object chứa isValid và message
 */
export const validateLabelName = (labelName) => {
    if (!labelName || typeof labelName !== 'string') {
        return {
            isValid: false,
            message: 'Tên label không được để trống'
        };
    }

    const { name } = parseEmojiFromLabel(labelName);
    
    if (!name.trim()) {
        return {
            isValid: false,
            message: 'Tên label không được để trống'
        };
    }

    if (name.trim().length > 50) {
        return {
            isValid: false,
            message: 'Tên label không được vượt quá 50 ký tự'
        };
    }

    return {
        isValid: true,
        message: ''
    };
};

export { SUPPORTED_EMOJIS };
