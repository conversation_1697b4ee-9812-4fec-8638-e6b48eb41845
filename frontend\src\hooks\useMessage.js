import { useCallback } from 'react';
import { message } from 'antd';

const useMessage = () => {
    const [messageApi, contextHolder] = message.useMessage();

    const showMessage = useCallback((type, content) => {
        messageApi.open({
            type,
            content,
            duration: 3
        });
    }, [messageApi]);

    return {
        contextHolder,
        showMessage
    };
}

export default useMessage;