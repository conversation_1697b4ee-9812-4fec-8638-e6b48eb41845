import axios from "../utils/axiosCustomize";

const getWorkspaces = async () => {
    const response = await axios.get("/workspace");
    return response.data;
};

const getWorkspaceById = async (id) => {
    const response = await axios.get(`/workspace/${id}`);
    return response.data;
};

const createWorkspace = async (workspaceData) => {
    const response = await axios.post("/workspace", workspaceData);
    return response.data;
};

const updateWorkspace = async (id, workspaceData) => {
    const response = await axios.put(`/workspace/${id}`, workspaceData);
    return response.data;
};

const deleteWorkspace = async (id) => {
    const response = await axios.delete(`/workspace/${id}`);
    return response.data;
};

const generatePublicShareId = async () => {
    const response = await axios.post("/workspace/generate-public-share-id");
    return response.data;
};

const enablePublicShare = async (id) => {
    const response = await axios.put(`/workspace/${id}/enable-public-share`);
    return response.data;
};

const disablePublicShare = async (id) => {
    const response = await axios.put(`/workspace/${id}/disable-public-share`);
    return response.data;
};

const getPublicWorkspace = async (publicShareId) => {
    const response = await axios.get(`/workspace/public/${publicShareId}`);
    return response.data;
};

const getPublicBoardTaskLists = async (publicShareId, boardId) => {
    const response = await axios.get(
        `/workspace/public/${publicShareId}/boards/${boardId}/tasklists`
    );
    return response.data;
};

export default {
    getWorkspaces,
    getWorkspaceById,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    generatePublicShareId,
    enablePublicShare,
    disablePublicShare,
    getPublicWorkspace,
    getPublicBoardTaskLists,
};
