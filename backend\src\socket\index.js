const e = require("express");
const { registerTaskSocketHandlers } = require("./taskHandle");
const { registerTaskListSocketHandlers } = require("./tasklistHandle");
const { registerWorkspaceSocketHandlers } = require("./workspaceHandle");
const { registerBoardSocketHandlers } = require("./boardHandle");
const { registerNotificationSocketHandlers } = require("./notificationHandle");

let io;

// Khởi tạo Socket.IO
function init(socketIo) {
    io = socketIo;

    // Xử lý kết nối socket
    io.on("connection", (socket) => {
        const count = getIO().engine.clientsCount;
        console.log(
            `Người dùng kết nối: ${socket.id} - Tổng số người dùng: ${count}`
        );

        //  Xử lý join workspace room
        socket.on("workspace:join", (workspaceId) => {
            const roomId = `workspace_${workspaceId}`;
            socket.join(roomId);
            console.log(
                `>> Client ${socket.id} joined workspace room ${roomId}`
            );
        });

        // X<PERSON> lý join board room
        socket.on("board:join", (boardId) => {
            const roomId = `board_${boardId}`;
            socket.join(roomId);
            console.log(`>> Client ${socket.id} joined board room ${roomId}`);
        });

        // Đăng ký các event socket
        registerWorkspaceSocketHandlers(socket, emitToWorkspace);
        registerBoardSocketHandlers(socket, emitToBoard);
        registerTaskSocketHandlers(socket, emitToBoard);
        registerTaskListSocketHandlers(socket, emitToBoard);
        registerNotificationSocketHandlers(socket, emitToUser);

        // Xử lý leave board room
        socket.on("board:leave", (boardId) => {
            const roomId = `board_${boardId}`;
            socket.leave(roomId);
            console.log(`>> Client ${socket.id} left board room ${roomId}`);
        });

        //Xử lý leave workspace room
        socket.on("workspace:leave", (workspaceId) => {
            const roomId = `workspace_${workspaceId}`;
            socket.leave(roomId);
            console.log(`>> Client ${socket.id} left workspace room ${roomId}`);
        });

        socket.on("disconnect", () => {
            const count = getIO().engine.clientsCount;
            console.log(
                `Người dùng ngắt kết nối: ${socket.id} - Tổng số người dùng: ${count}`
            );
        });
    });

    return io;
}

// Lấy đối tượng io đã khởi tạo
function getIO() {
    if (!io) {
        throw new Error("Socket.IO chưa được khởi tạo!");
    }
    return io;
}

// Gửi thông báo đến tất cả người dùng
function emitToAll(event, data) {
    getIO().emit(event, data);
}

// Gửi thông báo đến một workspace room
function emitToWorkspace(workspaceId, event, data) {
    const roomId = `workspace_${workspaceId}`;
    getIO().to(roomId).emit(event, data);
}

// Gửi thông báo đến một user
function emitToUser(userId, event, data) {
    getIO().to(userId).emit(event, data);
}

// Gửi thông báo đến một board room
function emitToBoard(boardId, event, data) {
    const roomId = `board_${boardId}`;
    getIO().to(roomId).emit(event, data);
}


module.exports = {
    init,
    getIO,
};
