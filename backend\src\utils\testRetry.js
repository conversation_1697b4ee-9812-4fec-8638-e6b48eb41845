const { retryGoogleCall } = require('./googleRetry');

// Test function mô phỏng Google API call
async function mockGoogleAPI() {
  const random = Math.random();
  if (random < 0.7) { // 70% chance of failure
    throw { response: { status: 500 }, message: 'Server Error' };
  }
  return { data: 'Success!' };
}

// Test thủ công
async function runTest() {
  try {
    console.log('🚀 Bắt đầu test...');
    
    const result = await retryGoogleCall(
      mockGoogleAPI,
      'test-manual',
      { defaultValue: 'Fallback data' }
    );

    console.log('📝 Kết quả:', result);
  } catch (error) {
    console.error('❌ Lỗi:', error);
  }
}

runTest(); 