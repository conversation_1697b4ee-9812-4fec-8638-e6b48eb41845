import dayjs from 'dayjs';

// Hàm format ngày theo Google Tasks style
export const formatDate = (date) => {
    if (!date) return '';
    
    const now = dayjs();
    const targetDate = dayjs(date);
    
    // So sánh ngày bằng cách sử dụng startOf('day') để loại bỏ giờ phút giây
    const nowStartOfDay = now.startOf('day');
    const targetStartOfDay = targetDate.startOf('day');
    const diffDays = targetStartOfDay.diff(nowStartOfDay, 'day');
    
    // Tính tuần dựa trên ngày bắt đầu của tuần
    const nowStartOfWeek = now.startOf('week');
    const targetStartOfWeek = targetDate.startOf('week');
    const diffWeeks = targetStartOfWeek.diff(nowStartOfWeek, 'week');
    
    // Nếu là ngày hôm nay
    if (diffDays === 0) {
        return `Hôm nay ${targetDate.format('HH:mm')}`;
    }
    
    // Nếu là ngày mai
    if (diffDays === 1) {
        return `Ngày mai ${targetDate.format('HH:mm')}`;
    }
    
    // Nếu là ngày hôm qua
    if (diffDays === -1) {
        return `Hôm qua ${targetDate.format('HH:mm')}`;
    }
    
    // Nếu là ngày trong tương lai gần (2-6 ngày)
    if (diffDays > 1 && diffDays <= 6) {
        const dayNames = ['Chủ nhật', 'Thứ hai', 'Thứ ba', 'Thứ tư', 'Thứ năm', 'Thứ sáu', 'Thứ bảy'];
        const dayName = dayNames[targetDate.day()];
        return `${dayName} ${targetDate.format('HH:mm')}`;
    }
    
    // Nếu là ngày trong quá khứ gần (2-6 ngày trước)
    if (diffDays < -1 && diffDays >= -6) {
        const dayNames = ['Chủ nhật', 'Thứ hai', 'Thứ ba', 'Thứ tư', 'Thứ năm', 'Thứ sáu', 'Thứ bảy'];
        const dayName = dayNames[targetDate.day()];
        return `${dayName} ${targetDate.format('HH:mm')}`;
    }
    
    // Nếu là tuần này (tương lai)
    if (diffWeeks === 0 && diffDays > 6) {
        return `Tuần này ${targetDate.format('DD/MM HH:mm')}`;
    }
    
    // Nếu là tuần trước (quá khứ)
    if (diffWeeks === -1) {
        return `Tuần trước ${targetDate.format('DD/MM HH:mm')}`;
    }
    
    // Nếu là tuần sau
    if (diffWeeks === 1) {
        return `Tuần sau ${targetDate.format('DD/MM HH:mm')}`;
    }
    
    // Nếu là ngày xa hơn, hiển thị theo format thông thường
    if (diffWeeks > 1) {
        return targetDate.format('DD/MM/YYYY HH:mm');
    }
    
    if (diffWeeks < -1) {
        return targetDate.format('DD/MM/YYYY HH:mm');
    }
    
    // Fallback
    return targetDate.format('DD/MM/YYYY HH:mm');
}; 