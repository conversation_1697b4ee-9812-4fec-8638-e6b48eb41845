const rateLimit = require('express-rate-limit');
const { body, param, validationResult } = require('express-validator');

// Rate limiting cho public endpoints
const publicAccessLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 phút
    max: 100, // Giới hạn 100 requests mỗi 15 phút cho mỗi IP
    message: {
        error: 'Quá nhiều yêu cầu từ IP này, vui lòng thử lại sau 15 phút'
    },
    standardHeaders: true,
    legacyHeaders: false,
});

// Rate limiting nghiêm ngặt hơn cho public workspace access
const publicWorkspaceLimiter = rateLimit({
    windowMs: 5 * 60 * 1000, // 5 phút
    max: 30, // Giớ<PERSON> hạn 30 requests mỗi 5 phút cho mỗi IP
    message: {
        error: 'Quá nhiều yêu cầu truy cập workspace công khai, vui lòng thử lại sau'
    },
    standardHeaders: true,
    legacyHeaders: false,
});

// Validation cho publicShareId
const validatePublicShareId = [
    param('publicShareId')
        .isUUID(4)
        .withMessage('ID chia sẻ không hợp lệ')
        .notEmpty()
        .withMessage('ID chia sẻ là bắt buộc'),
    
    (req, res, next) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                message: 'Dữ liệu không hợp lệ',
                errors: errors.array()
            });
        }
        next();
    }
];

// Middleware để log public access (cho monitoring)
const logPublicAccess = (req, res, next) => {
    const { publicShareId } = req.params;
    const userAgent = req.get('User-Agent');
    const ip = req.ip || req.connection.remoteAddress;
    
    console.log(`[PUBLIC ACCESS] ${new Date().toISOString()} - IP: ${ip}, ShareID: ${publicShareId}, UserAgent: ${userAgent}`);
    
    next();
};

// Security headers cho public endpoints
const addSecurityHeaders = (req, res, next) => {
    // Ngăn chặn clickjacking
    res.setHeader('X-Frame-Options', 'SAMEORIGIN');
    
    // Ngăn chặn MIME type sniffing
    res.setHeader('X-Content-Type-Options', 'nosniff');
    
    // XSS Protection
    res.setHeader('X-XSS-Protection', '1; mode=block');
    
    // Referrer Policy
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    next();
};

module.exports = {
    publicAccessLimiter,
    publicWorkspaceLimiter,
    validatePublicShareId,
    logPublicAccess,
    addSecurityHeaders
};
