const path = require('path');
const fs = require('fs');
const { AsyncService } = require('../services/async.service');

const LOCK_FILE = path.join('/tmp', 'autoAsync.lock');  // đ<PERSON>i thư mục tạm
const LOG_FILE = path.join('/tmp', 'autoAsync.log');    // đổi thư mục tạm
const MAX_EXECUTION_TIME = 3 * 60 * 1000; // 3 phút

function log(message, isError = false) {
    const timestamp = new Date().toISOString();
    const pid = process.pid;
    const logMessage = `[${timestamp}][PID:${pid}] ${message}\n`;

    const output = isError ? console.error : console.log;
    output(logMessage);

    try {
        fs.appendFileSync(LOG_FILE, logMessage);
    } catch (err) {
        console.error(`[${timestamp}][PID:${pid}] Ghi log thất bại: ${err.message}`);
    }
}

function isLocked() {
    return fs.existsSync(LOCK_FILE);
}

function lock() {
    fs.writeFileSync(LOCK_FILE, String(process.pid));
}

function unlock() {
    if (fs.existsSync(LOCK_FILE)) {
        fs.unlinkSync(LOCK_FILE);
    }
}

(async () => {
    const startTime = Date.now();

    if (isLocked()) {
        log('Đang có tiến trình khác đang chạy. Bỏ qua lần thực thi này.', true);
        return;
    }

    try {
        lock();
        log('Bắt đầu tự động đồng bộ dữ liệu với Google Task');

        const result = await AsyncService.autoSyncData();

        if (result && typeof result === 'object') {
            log('Đã hoàn thành tự động đồng bộ với Google Task');
        } else {
            log('Kết quả không hợp lệ hoặc không có kết quả trả về.', true);
        }

    } catch (error) {
        log(`Lỗi khi tự động đồng bộ với Google Task: ${error.message}`, true);
    } finally {
        unlock();
        const duration = Date.now() - startTime;
        if (duration > MAX_EXECUTION_TIME) {
            log(`Cảnh báo: Job chạy quá thời gian giới hạn: ${duration / 1000}s`, true);
        }
    }
})();
