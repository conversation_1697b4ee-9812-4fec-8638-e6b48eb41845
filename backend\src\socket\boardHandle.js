function registerBoardSocketHandlers (socket, emitToBoard) {
    // Lắng nghe sự kiện sau khi board đượ<PERSON> cập nhật từ API
    socket.on("board:updated", (data) => {
        const { boardId } = data;
        // console.log("Board updated event received:", data);
        emitToBoard(boardId, "board:updated", data);
    });

    // Lắng nghe sự kiện sau khi board được xóa từ API
    socket.on("board:deleted", (data) => {
        const { boardId } = data;
        // console.log("Board deleted event received:", data);
        emitToBoard(boardId, "board:deleted", { id: boardId });
    });

    // Lắng nghe sự kiện sau khi board được thêm vào board assign
    socket.on("board:assigned", (data) => {
        const { boardId } = data;
        // console.log("Board assigned event received:", data);
        emitToBoard(boardId, "board:assigned", { id: boardId });
    });

    // Lắng nghe sự kiện sau khi board đượ<PERSON> rời khỏi board assign
    socket.on("board:unassigned", (data) => {
        const { boardId } = data;
        // console.log("Board unassigned event received:", data);
        emitToBoard(boardId, "board:unassigned", { id: boardId });
    });
}

module.exports = { registerBoardSocketHandlers };