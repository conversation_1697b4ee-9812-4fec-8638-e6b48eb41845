{"name": "task", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon server.js", "start": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"compression": "^1.8.0", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "express-session": "^1.18.1", "express-validator": "^7.2.1", "fractional-indexing": "^3.2.0", "fs": "^0.0.1-security", "google-auth-library": "^10.1.0", "googleapis": "^150.0.1", "helmet": "^8.1.0", "https": "^1.0.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "mysql2": "^3.14.1", "node-cron": "^4.2.1", "p-retry": "^4.6.2", "p-timeout": "^6.1.4", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "path": "^0.12.7", "redis": "^5.5.6", "sequelize": "^6.37.7", "socket.io": "^4.8.1", "stream": "^0.0.3", "uuid": "^11.1.0"}, "devDependencies": {"jest": "^30.0.4", "nodemon": "^3.1.10"}}