const express = require('express');
const router = express.Router();
const { handleTokenExpiration } = require('../middleware/googleAuth');
const {
    getTaskLists,
    getAllTaskLists,
    createTaskList,
    updateTaskList,
    deleteTaskList,
    getTaskListByBoardId,
    moveTaskList
} = require('../controllers/taskListController');
const { isAuthenticated } = require('../middleware/auth');


// Task Lists routes
router.get('/', isAuthenticated, handleTokenExpiration, getTaskLists);
router.get('/all', isAuthenticated, handleTokenExpiration, getAllTaskLists);
router.post('/', isAuthenticated, handleTokenExpiration, createTaskList);
router.put('/:taskListId', isAuthenticated, handleTokenExpiration, updateTaskList);
router.put('/:taskListId/move', isAuthenticated, handleTokenExpiration, moveTaskList);
router.delete('/:taskListId', isAuthenticated, handleTokenExpiration, deleteTaskList);
router.get('/board/:boardId', isAuthenticated, handleTokenExpiration, getTaskListByBoardId);

module.exports = router; 