module.exports = (sequelize, DataTypes) => {
    const BoardAssignee = sequelize.define('BoardAssignee', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        board_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        assigned_at: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW
        }
    }, {
        tableName: 'board_assignees',
        timestamps: false
    });

    BoardAssignee.associate = (models) => {
        BoardAssignee.belongsTo(models.Board, {
                foreignKey: 'board_id',
                as: 'board',
                onDelete: 'CASCADE'
        });

        BoardAssignee.belongsTo(models.User, {
            foreignKey: 'user_id',
            as: 'user'
        });
    };

    return BoardAssignee;
};