function registerWorkspaceSocketHandlers (socket, emitToWorkspace) {
    // <PERSON><PERSON><PERSON> nghe sự kiện sau khi workspace đư<PERSON><PERSON> thêm vào workspace assign
    socket.on("workspace:assigned", (data) => {
        const { workspaceId } = data;
        // console.log("Workspace assigned event received:", data);
        emitToWorkspace(workspaceId, "workspace:assigned", { id: workspaceId });
    });

    // Lắng nghe sự kiện sau khi workspace đượ<PERSON> rời khỏi workspace assign
    socket.on("workspace:unassigned", (data) => {
        const { workspaceId } = data;
        // console.log("Workspace unassigned event received:", data);
        emitToWorkspace(workspaceId, "workspace:unassigned", { id: workspaceId });
    });

};

module.exports = { registerWorkspaceSocketHandlers };