const { Notification, User } = require('../models');

class NotificationService {
    async createNotification(data) {
        try {
            return await Notification.create(data);
        } catch (error) {
            throw error;
        }
    }

    async getNotificationsByUserId(userId) {
        try {
            return await Notification.findAll({
                where: { userId },
                include: [{
                    model: User,
                    as: 'user',
                    attributes: ['id', 'fullName', 'email']
                }],
                order: [['createdAt', 'DESC']]
            });
        } catch (error) {
            throw error;
        }
    }

    async deleteNotification(notificationId, userId) {
        try {
            const result = await Notification.destroy({
                where: { id: notificationId, userId }
            });
            
            if (!result) {
                throw new Error('Notification not found');
            }
            
            return result;
        } catch (error) {
            throw error;
        }
    }

    async markAsRead(notificationId, userId) {
        try {
            const result = await Notification.update({ isRead: true }, { where: { id: notificationId, userId } });

            if (!result) {
                throw new Error('Notification not found');
            }

            return result;
        } catch (error) {
            throw error;
        }
    }

    async markAllAsRead(userId) {
        try {
            const result = await Notification.update({ isRead: true }, { where: { userId } });

            if (!result) {
                throw new Error('No notifications found');
            }

            return result;
        } catch (error) {
            throw error;
        }
    }

    async deleteAllNotifications(userId) {
        try {
            const result = await Notification.destroy({ where: { userId } });
            return result;
        } catch (error) {
            throw error;
        }
    }
}

module.exports = new NotificationService(); 