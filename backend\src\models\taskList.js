
module.exports = (sequelize, DataTypes) => {
  const TaskList = sequelize.define('TaskList', {
    id: {
      type: DataTypes.STRING(255),
      primaryKey: true
    },
    title: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT
    },
    ownerId: {
      type: DataTypes.INTEGER,
      field: 'owner_id'
    },
    boardId: {
      type: DataTypes.INTEGER,
      field: 'board_id'
    },
    googleTaskListId: {
      type: DataTypes.STRING(255),
      default: null
    },
    position: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    isDefault: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
  }, {
    tableName: 'task_lists',
    timestamps: false
  });

  TaskList.associate = (models) => {
    TaskList.belongsTo(models.User, {
      foreignKey: 'owner_id',
      as: 'owner'
    });
    TaskList.belongsTo(models.Board, {
      foreignKey: 'board_id',
      as: 'board',
      onDelete: 'CASCADE'
    });
    TaskList.hasMany(models.Task, {
      foreignKey: 'tasklist_id',
      as: 'tasks',
      onDelete: 'CASCADE'
    });
  };

  return TaskList;
};