const router = require('express').Router();
const { addAssigneeToTask, removeAssigneeFromTask, getTaskAssignees, updateTaskAssignees, clearTaskAssignees, getUserAssignedTasks } = require('../controllers/taskAssignController');
const { isAuthenticated } = require('../middleware/auth');
const { handleTokenExpiration } = require('../middleware/googleAuth');

router.post('/add-assignee', isAuthenticated, handleTokenExpiration, addAssigneeToTask);
router.post('/remove-assignee', isAuthenticated, handleTokenExpiration, removeAssigneeFromTask);
router.get('/user-assigned-tasks', isAuthenticated, handleTokenExpiration, getUserAssignedTasks);
router.get('/:taskId', isAuthenticated, handleTokenExpiration, getTaskAssignees);
router.post('/update-assignees', isAuthenticated, handleTokenExpiration, updateTaskAssignees);
router.delete('/clear-assignees/:taskId', isAuthenticated, handleTokenExpiration, clearTaskAssignees);


module.exports = router;