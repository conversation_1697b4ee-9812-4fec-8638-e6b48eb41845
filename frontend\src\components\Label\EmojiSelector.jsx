import React, { useState } from "react";
import { But<PERSON>, Popover } from "antd";
import { SmileOutlined } from "@ant-design/icons";
import "./LabelComponents.css";

// Danh sách emoji cố định
const EMOJI_LIST = [
    "🔥", // Fire - Urgent/Hot
    "✅", // Check mark - Done/Completed
    "🐛", // Bug - Bug/Issue
    "🚩", // Flag - Important/Warning
    "📌", // Pin - Pinned/Important
    "🎯", // Target - Goal/Objective
    "⚡", // Lightning - Fast/Quick
    "🔧", // Wrench - Fix/Maintenance
    "💡", // Bulb - Idea/Feature
    "📝", // Memo - Documentation
    "🚀", // Rocket - Launch/Deploy
    "⭐", // Star - Favorite/Priority
    "🔒", // Lock - Security/Private
    "🌟", // Glowing star - Special
    "📊", // Chart - Analytics/Data
    "🎨", // Palette - Design/UI
    "🔍", // Magnifying glass - Research/Investigation
    "💻", // Laptop - Development
    "📱", // Mobile - Mobile/App
    "🌐", // Globe - Web/Global
];

const EmojiPicker = ({
    selectedEmoji,
    onEmojiSelect,
    style,
    className,
    disabled = false,
}) => {
    const [open, setOpen] = useState(false);

    const handleEmojiClick = (emoji) => {
        onEmojiSelect(emoji);
        setOpen(false);
    };

    const handleClearEmoji = () => {
        onEmojiSelect("");
        setOpen(false);
    };

    const emojiPickerContent = (
        <div className="emoji-picker-content">
            <div className="emoji-grid">
                {EMOJI_LIST.map((emoji) => (
                    <Button
                        key={emoji}
                        type={selectedEmoji === emoji ? "primary" : "default"}
                        size="small"
                        className={`emoji-button ${
                            selectedEmoji === emoji ? "selected" : ""
                        }`}
                        onClick={() => handleEmojiClick(emoji)}
                        style={{
                            width: "32px",
                            height: "32px",
                            padding: "0",
                            fontSize: "16px",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            margin: "2px",
                        }}
                    >
                        {emoji}
                    </Button>
                ))}
            </div>
            {selectedEmoji && (
                <div
                    style={{
                        marginTop: "8px",
                        textAlign: "center",
                        borderTop: "1px solid #f0f0f0",
                        paddingTop: "8px",
                    }}
                >
                    <Button
                        type="text"
                        size="small"
                        onClick={handleClearEmoji}
                        style={{ color: "#999" }}
                    >
                        Xóa
                    </Button>
                </div>
            )}
        </div>
    );

    return (
        <Popover
            content={emojiPickerContent}
            title="Chọn emoji"
            trigger="click"
            open={open}
            onOpenChange={setOpen}
            placement="bottomLeft"
            className="emoji-picker-popover"
        >
            <Button
                className={`emoji-picker-trigger ${className || ""}`}
                style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    ...style,
                }}
                disabled={disabled}
                title={selectedEmoji ? `Emoji: ${selectedEmoji}` : "Chọn emoji"}
            >
                {selectedEmoji ? (
                    <span style={{ fontSize: "16px" }}>{selectedEmoji}</span>
                ) : (
                    <SmileOutlined style={{ fontSize: "16px" }} />
                )}
            </Button>
        </Popover>
    );
};

export default EmojiPicker;
