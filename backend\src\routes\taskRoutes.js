const express = require('express');
const router = express.Router();
const { handleTokenExpiration } = require('../middleware/googleAuth');
const {
  getAllTasks,
  getTask,
  createTask,
  createSubtask,
  updateTask,
  completeTask,
  unCompleteTask,
  deleteTask,
  clearCompletedTasks,
  moveTask,
  searchTasks
} = require('../controllers/taskController');
const { isAuthenticated } = require('../middleware/auth');

// Tasks routes
router.get('/:taskListId/tasks', isAuthenticated, handleTokenExpiration, getAllTasks);
router.get('/:taskListId/tasks/:taskId', isAuthenticated, handleTokenExpiration, getTask);
router.post('/:taskListId/tasks', isAuthenticated, handleTokenExpiration, createTask);
router.post('/:taskListId/tasks/:parentTaskId/subtask', isAuthenticated, handleTokenExpiration, createSubtask);
router.put('/:taskListId/tasks/:taskId', isAuthenticated, handleTokenExpiration, updateTask);
router.put('/:taskListId/tasks/:taskId/complete', isAuthenticated, handleTokenExpiration, completeTask);
router.put('/:taskListId/tasks/:taskId/uncomplete', isAuthenticated, handleTokenExpiration, unCompleteTask);
router.delete('/:taskListId/tasks/:taskId', isAuthenticated, handleTokenExpiration, deleteTask);
router.post('/:taskListId/tasks/clear', isAuthenticated, handleTokenExpiration, clearCompletedTasks);
router.post('/:taskListId/tasks/:taskId/move', isAuthenticated, handleTokenExpiration, moveTask);
router.get('/tasks/search', isAuthenticated, handleTokenExpiration, searchTasks);

module.exports = router; 