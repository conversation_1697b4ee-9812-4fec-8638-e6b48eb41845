module.exports = (sequelize, DataTypes) => {
    const Board = sequelize.define(
        "Board",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },
            name: {
                type: DataTypes.STRING(100),
                allowNull: false,
            },
            workspaceId: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            createdBy: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            isGoogleSynced: {
                type: DataTypes.BOOLEAN,
                allowNull: false,
                defaultValue: false,
            },
            isDefault: {
                type: DataTypes.BOOLEAN,
                allowNull: false,
                defaultValue: false,
            },
        },
        {
            tableName: "boards",
            timestamps: true,
        }
    );

    Board.associate = (models) => {
        Board.belongsTo(models.User, {
            foreignKey: "createdBy",
            as: "createdByUser",
        });
        Board.hasMany(models.TaskList, {
            foreignKey: "board_id",
            as: "taskList",
            onDelete: "CASCADE",
        });
        Board.hasMany(models.BoardAssignee, {
            foreignKey: "board_id",
            as: "boardAssignees",
            onDelete: "CASCADE",
        });
        Board.belongsTo(models.Workspace, {
            foreignKey: "workspaceId",
            as: "workspace",
        });
        Board.hasMany(models.Label, {
            foreignKey: "board_id",
            as: "labels",
            onDelete: "CASCADE",
        });
    };

    return Board;
};
