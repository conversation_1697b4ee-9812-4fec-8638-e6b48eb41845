const multer = require('multer');
const path = require('path');

//<PERSON><PERSON><PERSON> hình nơi lưu và tên file
const storage = multer.memoryStorage({
    destination: (req, file, cb) => {
        cb(null, 'uploads/');
    },
    filename: (req, file, cb) => {
        const extension = path.extname(file.originalname);
        const filename = `${Date.now()}-${file.originalname.split('.')[0]}`;
        cb(null, `${filename}${extension}`);
    }
})

const upload = multer({ 
    storage, 
    limits: { fileSize: 50 * 1024 * 1024 }  //Giới hạn upload 50MB
});

module.exports = upload;