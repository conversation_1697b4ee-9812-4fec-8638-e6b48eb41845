const { addAssigneeBoard, removeAssigneeFromBoard: removeAssigneeService, getBoardAssignees: getBoardAssigneesService, getUserAssignedBoards } = require('../services/boardAssignService');

const addAssigneeToBoard = async (req, res) => {
    try {
        const { boardId, email } = req.body;
        const assignee = await addAssigneeBoard(boardId, email);
        res.status(200).json(assignee);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
}

const removeAssigneeFromBoard = async (req, res) => {
    try {
        const { boardId, email } = req.body;
        
        const assignee = await removeAssigneeService(boardId, email);
        res.status(200).json(assignee);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
}

const getBoardAssignees = async (req, res) => {
    try {
        const { boardId } = req.params;
        const assignees = await getBoardAssigneesService(boardId);
        res.status(200).json(assignees);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
}

const getUserBoards = async (req, res) => {
    try {
        const userId = req.user.id;
        const boards = await getUserAssignedBoards(userId);
        res.status(200).json(boards);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
}

module.exports = {
    addAssigneeToBoard,
    removeAssigneeFromBoard,
    getBoardAssignees,
    getUserBoards
}