const {
    getAuthenticatedClient,
    refreshAccessToken,
} = require("../middleware/googleAuth");
const { Task, TaskList, User } = require("../models");
const { v4: uuidv4 } = require("uuid");

class GoogleTaskListService {
    async createTaskList(data) {
        try {
            const client = await getAuthenticatedClient(data.userId);

            const tasklist = await client.tasklists.insert({
                requestBody: {
                  title: data.taskList.title,
                },
              });

            // Cập nhật ID từ Google Tasks vào database
            await TaskList.update(
                { googleTaskListId: tasklist.data.id },
                { where: { id: data.taskList.id } }
            );
        } catch (error) {
            if(error.code === 401) {
                console.warn("Access token có thể đã hết hạn, thử làm mới...");
                const newAccessToken = await refreshAccessToken(data.userId);
                if(newAccessToken) {
                    console.log("Làm mới access token thành công"); 
                    return this.createTaskList(data);
                }else {
                    console.error("Làm mới access token thất bại");
                }
            }else if(error.code === 404) {
                console.error("Error creating Google Task List:", error);
                throw error;
            }else {
                console.error("Error creating Google Task List:", error);
                throw error;
            }
        }
    }

    async updateTaskList(data) {
        try {
            const client = await getAuthenticatedClient(data.userId);
            const existingTaskList = await TaskList.findByPk(data.taskList.id);

            await client.tasklists.update({
                tasklist: existingTaskList.googleTaskListId,
                requestBody: {
                    id: existingTaskList.googleTaskListId,
                    title: data.taskList.title,
                },
            });
        } catch (error) {
            if(error.code === 401) {
                console.warn("Access token có thể đã hết hạn, thử làm mới...");
                const newAccessToken = await refreshAccessToken(data.userId);
                if(newAccessToken) {
                    console.log("Làm mới access token thành công"); 
                    return this.updateTaskList(data);
                }else {
                    console.error("Làm mới access token thất bại");
                }
            }else {
                console.error("Error updating Google Task List:", error);
                throw error;
            }
        }
    }

    async deleteTaskList(data) {
        try {
            const client = await getAuthenticatedClient(data.userId);
            const existingTaskList = await TaskList.findByPk(data.taskListId);

            await client.tasklists.delete({
                tasklist: existingTaskList.googleTaskListId,
            });
        } catch (error) {
            if(error.code === 401) {
                console.warn("Access token có thể đã hết hạn, thử làm mới...");
                const newAccessToken = await refreshAccessToken(data.userId);
                if(newAccessToken) {
                    console.log("Làm mới access token thành công"); 
                    return this.deleteTaskList(data);
                }else {
                    console.error("Làm mới access token thất bại");
                }
            }else {
                console.error("Error deleting Google Task List:", error);
                throw error;
            }
        }
    }
}

module.exports = new GoogleTaskListService();
