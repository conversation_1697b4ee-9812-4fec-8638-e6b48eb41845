function registerNotificationSocketHandlers(socket, emitToUser) {
    // <PERSON><PERSON><PERSON> nghe sự kiện sau khi notification được tạo từ API
    socket.on("notification:created", (data) => {
        const { notification } = data;
        // console.log("Notification created event received:", data);
        emitToUser(notification.userId, "notification:created", notification);
    });

    // Lắng nghe sự kiện sau khi notification được cập nhật từ API
    socket.on("notification:updated", (data) => {
        const { notification } = data;
        // console.log("Notification updated event received:", data);
        emitToUser(notification.userId, "notification:updated", notification);
    });
}

module.exports = {
    registerNotificationSocketHandlers
}