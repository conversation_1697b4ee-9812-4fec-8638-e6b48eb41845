const taskService = require("../services/taskService");
const GoogleTaskService = require("../services/googleTaskService");
const taskListService = require("../services/taskListService");
const userService = require("../services/userService");

// L<PERSON>y danh sách tasks trong một task list
const getAllTasks = async (req, res) => {
    try {
        const { taskListId } = req.params;
        const tasks = await taskService.getAllTasks(taskListId);
        res.json(tasks);
    } catch (error) {
        console.error("Lỗi khi lấy tasks:", error);
        res.status(500).json({
            message: "Lỗi khi lấy tasks",
            error: error.message,
        });
    }
};

//Lấy tác vụ đã chỉ định
const getTask = async (req, res) => {
    try {
        const { taskId } = req.params;
        const boardId = req.query.boardId;

        const task = await taskService.getTask(taskId);
        if (!task) {
            return res.status(404).json({ message: "Task không tồn tại" });
        }

        res.status(200).json(task);
    } catch (error) {
        console.error("Lỗi khi lấy task:", error);
        res.status(500).json({
            message: "Lỗi khi lấy task",
            error: error.message,
        });
    }
};

const createTask = async (req, res) => {
    try {
        const { taskListId } = req.params;
        const { newTask, boardId } = req.body;

        if (!newTask || !newTask.title || !boardId) {
            return res.status(400).json({
                message: "Thiếu dữ liệu bắt buộc: title hoặc boardId.",
            });
        }

        //Lấy thông tin của user creator từ taskList
        const taskList = await taskListService.getTaskListById(taskListId);
        const user = await userService.getUserById(taskList.ownerId);

        await taskService.markBoardForSync(boardId);
        const createdTask = await taskService.createTask(
            taskListId,
            newTask,
            user.id
        );

        //Gọi api đồng bộ với Google Task
        await GoogleTaskService.createTask({
            newTask: createdTask,
            taskListId: taskListId,
            userId: user.id,
        });

        return res.status(201).json(createdTask);
    } catch (error) {
        console.error("Lỗi khi tạo task:", error);
        return res.status(error.code).json({
            message: "Lỗi khi tạo task",
            error: error.message,
        });
    }
};

// Tạo task con (chỉ 1 cấp)
const createSubtask = async (req, res) => {
    try {
        const { taskListId, parentTaskId } = req.params;
        const { newTask, boardId } = req.body;

        if (!newTask || !newTask.title || !boardId) {
            return res.status(400).json({
                message: "Thiếu dữ liệu bắt buộc: title hoặc boardId.",
            });
        }

        //Lấy thông tin của user creator từ taskList
        const taskList = await taskListService.getTaskListById(taskListId);
        const user = await userService.getUserById(taskList.ownerId);

        await taskService.markBoardForSync(boardId);
        const createdTask = await taskService.createSubtask(
            taskListId,
            parentTaskId,
            newTask,
            user.id
        );

        //Gọi api đồng bộ với Google Task
        await GoogleTaskService.createSubtask({
            newTask: createdTask,
            taskListId: taskListId,
            parentTaskId: parentTaskId,
            userId: user.id,
        });

        return res.status(201).json(createdTask);
    } catch (error) {
        console.error("Lỗi khi tạo task con:", error);
        return res.status(error.code).json({
            message: error.message || "Lỗi khi tạo task con",
            error: error.message,
        });
    }
};

// Cập nhật task
const updateTask = async (req, res) => {
    try {
        const { taskId } = req.params;
        const { boardId, ...taskFields } = req.body;

        await taskService.markBoardForSync(boardId);
        const updatedTaskData = await taskService.updateTask(
            taskId,
            taskFields
        );

        //Lấy thông tin của user creator từ task
        const task = await taskService.getTask(taskId);
        const user = await userService.getUserById(task.createdBy);

        //Gọi api đồng bộ với Google Task
        await GoogleTaskService.updateTask({
            taskId: taskId,
            taskFields: taskFields,
            userId: user.id,
        });

        res.status(200).json(updatedTaskData);
    } catch (error) {
        console.error("Lỗi khi cập nhật task:", error);
        res.status(error.code).json({
            message: "Lỗi khi cập nhật task",
            error: error.message,
        });
    }
};

// Đánh dấu task là hoàn thành
const completeTask = async (req, res) => {
    try {
        const { taskId } = req.params;
        const boardId = req.body.boardId;

        await taskService.markBoardForSync(boardId);
        const updatedTask = await taskService.completeTask(taskId);

        //Lấy thông tin của user creator từ task
        const task = await taskService.getTask(taskId);
        const user = await userService.getUserById(task.createdBy);

        //Gọi api đồng bộ với Google Task
        await GoogleTaskService.completeTask({
            taskId: taskId,
            userId: user.id,
        });

        res.json(updatedTask);
    } catch (error) {
        console.error("Lỗi khi đánh dấu task hoàn thành:", error);
        res.status(error.code).json({
            message: "Lỗi khi đánh dấu task hoàn thành",
            error: error.message,
        });
    }
};

//Đánh dấu task là chưa hoàn thành
const unCompleteTask = async (req, res) => {
    try {
        const { taskId } = req.params;
        const boardId = req.body.boardId;

        await taskService.markBoardForSync(boardId);
        const updatedTask = await taskService.unCompleteTask(taskId);

        //Lấy thông tin của user creator từ task
        const task = await taskService.getTask(taskId);
        const user = await userService.getUserById(task.createdBy);

        //Gọi api đồng bộ với Google Task
        await GoogleTaskService.unCompleteTask({
            taskId: taskId,
            userId: user.id,
        });

        res.json(updatedTask);
    } catch (error) {
        console.error("Lỗi khi đánh dấu task chưa hoàn thành:", error);
        res.status(error.code).json({
            message: "Lỗi khi đánh dấu task chưa hoàn thành",
            error: error.message,
        });
    }
};

// Xóa task
const deleteTask = async (req, res) => {
    try {
        const { taskId } = req.params;
        const boardId = req.body.boardId;

        await taskService.markBoardForSync(boardId);

        //Lấy thông tin của user creator từ task
        const task = await taskService.getTask(taskId);
        const user = await userService.getUserById(task.createdBy);

        //Gọi api đồng bộ với Google Task
        await GoogleTaskService.deleteTask({
            taskId: taskId,
            userId: user.id,
        });

        await taskService.deleteTask(taskId);

        res.status(204).send();
    } catch (error) {
        console.error("Lỗi khi xóa task:", error);

        res.status(error.code).json({
            message: "Lỗi khi xóa task",
            error: error.message,
        });
    }
};

//Move task
const moveTask = async (req, res) => {
    try {
        const { taskId } = req.params;
        const { newTaskListId, previous, next, parent, boardId } = req.body;

        await taskService.markBoardForSync(boardId);
        const updatedTask = await taskService.moveTask(
            taskId,
            newTaskListId,
            parent,
            previous,
            next
        );
        res.json(updatedTask);
    } catch (error) {
        console.error("Lỗi khi di chuyển task:", error);
        res.status(error.code).json({
            message: "Lỗi khi di chuyển task",
            error: error.message,
        });
    }
};

//Xoá tất cả việc cần làm đã hoàn thành
const clearCompletedTasks = async (req, res) => {
    try {
        const taskListId = req.params.taskListId;

        await taskService.clearCompletedTasks(taskListId);
        res.status(200).json({
            message: "Đã xóa tất cả việc cần làm đã hoàn thành",
            taskListId: taskListId,
        });
    } catch (error) {
        console.error("Lỗi khi xóa task đã hoàn thành:", error);
        res.status(error.code).json({
            message: "Lỗi khi xóa task",
            error: error.message,
        });
    }
};

//Tìm kiếm task theo boardId và searchQuery
const searchTasks = async (req, res) => {
    try {
        const { query, boardId } = req.query;

        const groupedTasks = await taskService.searchTasks(query, boardId);
        res.status(200).json({
            status: "success",
            data: groupedTasks,
        });
    } catch (error) {
        console.error("Lỗi khi tìm kiếm tasks:", error);
        res.status(error.code).json({
            message: "Lỗi khi tìm kiếm tasks",
            error: error.message,
        });
    }
};

module.exports = {
    getAllTasks,
    getTask,
    createTask,
    createSubtask,
    updateTask,
    completeTask,
    unCompleteTask,
    deleteTask,
    clearCompletedTasks,
    moveTask,
    searchTasks,
};
