import React from 'react';
import { Card } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

const AddTaskCard = ({ listId, onAddTask }) => {
    return (
        <Card
            className='add-task-card'
            size='small'
            style={{ marginBottom: 8 }}
            hoverable
            onClick={() => onAddTask(listId)}
        >
            <div className='add-task-content'>
                <PlusOutlined style={{ fontSize: '16px', color: '#8c8c8c' }} />
                <span className='add-task-text'>Thêm task mới</span>
            </div>
        </Card>
    );
};

export default AddTaskCard;
