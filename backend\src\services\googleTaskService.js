const {
    getAuthenticatedClient,
    refreshAccessToken,
} = require("../middleware/googleAuth");
const { Task, TaskList, User } = require("../models");
const { v4: uuidv4 } = require("uuid");

class GoogleTaskService {
    async createTask(task) {
        try {
            const client = await getAuthenticatedClient(task.userId);

            const taskList = await TaskList.findByPk(task.taskListId);

            const newTask = await client.tasks.insert({
                tasklist: taskList.googleTaskListId,
                requestBody: {
                    title: task.newTask.title,
                    notes: task.newTask.notes || "",
                },
            });

            // Cập nhật ID từ Google Tasks vào database
            await Task.update(
                { googleTaskId: newTask.data.id },
                { where: { id: task.newTask.id } }
            );
        } catch (error) {
            if(error.code === 401) {
                console.warn("Access token có thể đã hết hạn, thử làm mới...");
                const newAccessToken = await refreshAccessToken(task.userId);
                if(newAccessToken) {
                    console.log("Làm mới access token thành công");
                    return this.createTask(task);
                }else {
                    console.error("Làm mới access token thất bại");
                }
            }else {
                console.error("Error creating Google Task:", error);
                throw error;
            }
        }
    }

    async createSubtask(task) {
        try {
            const client = await getAuthenticatedClient(task.userId);

            const taskList = await TaskList.findByPk(task.taskListId);

            const parentTask = await Task.findByPk(task.parentTaskId);

            const newTask = await client.tasks.insert({
                tasklist: taskList.googleTaskListId,
                parent: parentTask.googleTaskId,
                requestBody: {
                    title: task.newTask.title,
                    notes: task.newTask.notes || "",
                },
            });

            // Cập nhật ID từ Google Tasks vào database
            await Task.update(
                {
                    googleTaskId: newTask.data.id,
                    parentTaskId: task.parentTaskId,
                },
                { where: { id: task.newTask.id } }
            );
        } catch (error) {
            if(error.code === 401) {
                console.warn("Access token có thể đã hết hạn, thử làm mới...");
                const newAccessToken = await refreshAccessToken(task.userId);
                if(newAccessToken) {
                    console.log("Làm mới access token thành công");
                    return this.createSubtask(task);
                }else {
                    console.error("Làm mới access token thất bại");
                }
            }else {
                console.error("Error creating Google Task:", error);
                throw error;
            }
        }
    }

    async updateTask(task) {
        try {
            const client = await getAuthenticatedClient(task.userId);

            const taskDb = await Task.findByPk(task.taskId);

            const taskList = await TaskList.findByPk(taskDb.tasklist_id);

            console.log(">> task.taskFields:", task.taskFields);

            const res = await client.tasks.patch({
                tasklist: taskList.googleTaskListId,
                task: taskDb.googleTaskId,
                requestBody: {
                    ...task.taskFields,
                },
            });
            console.log(">> res:", res.data.due);
        } catch (error) {
            if(error.code === 401) {
                console.warn("Access token có thể đã hết hạn, thử làm mới...");
                const newAccessToken = await refreshAccessToken(task.userId);
                if(newAccessToken) {
                    console.log("Làm mới access token thành công");
                    return this.updateTask(task);
                }else {
                    console.error("Làm mới access token thất bại");
                }
            }else {
                console.error("Error updating Google Task:", error);
                throw error;
            }
        }
    }

    async deleteTask(task) {
        try {
            const client = await getAuthenticatedClient(task.userId);

            const taskDb = await Task.findByPk(task.taskId);

            const taskList = await TaskList.findByPk(taskDb.tasklist_id);

            const res = await client.tasks.delete({
                tasklist: taskList.googleTaskListId,
                task: taskDb.googleTaskId,
            });

        } catch (error) {
            if(error.code === 401) {
                console.warn("Access token có thể đã hết hạn, thử làm mới...");
                const newAccessToken = await refreshAccessToken(task.userId);
                if(newAccessToken) {
                    console.log("Làm mới access token thành công");
                    return this.deleteTask(task);
                }else {
                    console.error("Làm mới access token thất bại");
                }
            }else {
                console.error("Error deleting Google Task:", error);
                throw error;
            }
        }
    }

    async completeTask(task) {
        try {
            const client = await getAuthenticatedClient(task.userId);

            const taskDb = await Task.findByPk(task.taskId);

            const taskList = await TaskList.findByPk(taskDb.tasklist_id);

            await client.tasks.patch({
                tasklist: taskList.googleTaskListId,
                task: taskDb.googleTaskId,
                requestBody: {
                    status: "completed",
                    completed: new Date().toISOString(),
                },
            });
        } catch (error) {
            if(error.code === 401) {
                console.warn("Access token có thể đã hết hạn, thử làm mới...");
                const newAccessToken = await refreshAccessToken(task.userId);
                if(newAccessToken) {
                    console.log("Làm mới access token thành công");
                    return this.completeTask(task);
                }else {
                    console.error("Làm mới access token thất bại");
                }
            }else {
                console.error("Error completing Google Task:", error);
                throw error;
            }
        }
    }

    async unCompleteTask(task) {
        try {
            const client = await getAuthenticatedClient(task.userId);

            const taskDb = await Task.findByPk(task.taskId);

            const taskList = await TaskList.findByPk(taskDb.tasklist_id);

            await client.tasks.patch({
                tasklist: taskList.googleTaskListId,
                task: taskDb.googleTaskId,
                requestBody: {
                    status: "needsAction",
                    completed: null,
                },
            });
        } catch (error) {
            if(error.code === 401) {
                console.warn("Access token có thể đã hết hạn, thử làm mới...");
                const newAccessToken = await refreshAccessToken(task.userId);
                if(newAccessToken) {
                    console.log("Làm mới access token thành công");
                    return this.unCompleteTask(task);
                }else {
                    console.error("Làm mới access token thất bại");
                }
            }else {
                console.error("Error uncompleting Google Task:", error);
                throw error;
            }
        }
    }
}

module.exports = new GoogleTaskService();
