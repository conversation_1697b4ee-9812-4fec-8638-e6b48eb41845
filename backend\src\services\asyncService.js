const {getAuthenticatedClient} = require('../middleware/googleAuth');
const {Task, TaskList, Board, sequelize} = require('../models');
const {retryGoogleCall} = require('../utils/googleRetry');
const { v4: uuidv4 } = require('uuid');
const { Op } = require('sequelize');
const { formatDateForGoogleTasks } = require('../utils/date');

class AsyncService {
    // Đồng bộ tasklist từ database lên Google Tasks
    static async syncTaskListToGoogle(taskListId, userId, forceUpdate = false) { 
        try {
            const taskList = await TaskList.findByPk(taskListId);
            if (!taskList) throw new Error('TaskList không tồn tại');

            const googleTasks = await getAuthenticatedClient(userId);
            let googleTaskList;

            if (taskList.googleTaskListId) {
                try {
                    //console.log('>> update tasklist:', taskList.googleTaskListId);
                    //Kiểm tra tasklist tồn tại trước khi update
                    const taskListExists = await googleTasks.tasklists.get({
                        tasklist: taskList.googleTaskListId
                    });
                    if (!taskListExists) {
                        console.warn(`Google TaskList không tồn tại. Xoá googleTaskListId.`);
                        await TaskList.destroy({ where: { id: taskListId } });
                        return null;
                    }
    
                    //Nếu tồn tại → update
                    googleTaskList = await retryGoogleCall(
                        () => googleTasks.tasklists.update({
                            tasklist: taskList.googleTaskListId,
                            requestBody: {
                                id: taskList.googleTaskListId,
                                title: taskList.title 
                            }
                        }),
                        `update task list "${taskList.title}"`,
                        { taskListId: taskList.id, title: taskList.title, userId }
                    );
    
                } catch (error) {
                    if (error.response?.status === 404) {
                        console.warn(`Google TaskList không tồn tại. Xoá googleTaskListId.`);
    
                        await TaskList.destroy({ where: { id: taskListId } });
    
                        return null;
                    } else {
                        throw error;
                    }
                }
            } else {
                //console.log('>> insert tasklist:', taskList.title);
                googleTaskList = await retryGoogleCall(
                    () => googleTasks.tasklists.insert({
                        requestBody: { title: taskList.title }
                    }),
                    `insert task list "${taskList.title}"`,
                    { taskListId: taskList.id, title: taskList.title, userId }
                );
                await TaskList.update(
                    { googleTaskListId: googleTaskList.data.id },
                    { where: { id: taskListId } }
                );
            }

            return googleTaskList.data;
        } catch (error) {
            console.error('Lỗi khi đồng bộ task list:', error);
            throw error;
        }
    }

    // Đồng bộ tasklist từ Google Tasks về database
    static async syncTaskListFromGoogle(googleTaskListId, boardId, userId) {
        try {
            const googleTasks = await getAuthenticatedClient(userId);
            
            // Lấy thông tin tasklist từ Google Tasks
            const googleTaskList = await retryGoogleCall(
                () => googleTasks.tasklists.get({
                    tasklist: googleTaskListId
                }),
                `get task list from Google`,
                { googleTaskListId, userId }
            );

            // Kiểm tra xem tasklist đã tồn tại trong database chưa
            let taskList = await TaskList.findOne({
                where: { googleTaskListId: googleTaskListId }
            });

            if (taskList) {
                // Cập nhật tasklist hiện có
                await taskList.update({
                    title: googleTaskList.data.title,
                    updatedAt: new Date()
                });
                console.log(`Cập nhật tasklist: ${googleTaskList.data.title}`);
            } else {
                // Tạo tasklist mới
                taskList = await TaskList.create({
                    id: uuidv4(),
                    title: googleTaskList.data.title,
                    board_id: boardId,
                    googleTaskListId: googleTaskListId,
                    position: 0 // Có thể cần logic để tính position
                });
                console.log(`Tạo mới tasklist: ${googleTaskList.data.title}`);
            }

            return taskList;
        } catch (error) {
            console.error('Lỗi khi đồng bộ tasklist từ Google:', error);
            throw error;
        }
    }

    // Đồng bộ tất cả tasklist từ Google Tasks về database cho một board
    static async syncAllTaskListsFromGoogle(boardId, userId) {
        try {
            const googleTasks = await getAuthenticatedClient(userId);
            
            // Lấy tất cả tasklist từ Google Tasks
            const googleTaskLists = await retryGoogleCall(
                () => googleTasks.tasklists.list(),
                `list all task lists from Google`,
                { userId }
            );

            const syncedTaskLists = [];
            
            for (const googleTaskList of googleTaskLists.data.items) {
                try {
                    const taskList = await this.syncTaskListFromGoogle(
                        googleTaskList.id, 
                        boardId, 
                        userId
                    );
                    syncedTaskLists.push(taskList);
                } catch (error) {
                    console.error(`Lỗi khi đồng bộ tasklist ${googleTaskList.id}:`, error);
                }
            }

            return syncedTaskLists;
        } catch (error) {
            console.error('Lỗi khi đồng bộ tất cả tasklist từ Google:', error);
            throw error;
        }
    }

    // Đồng bộ hai chiều cho tasklist
    static async syncTaskListBidirectional(taskListId, userId, forceUpdate = false) {
        try {
            const taskList = await TaskList.findByPk(taskListId);
            if (!taskList) throw new Error('TaskList không tồn tại');

            // Đồng bộ từ database lên Google Tasks
            const googleTaskList = await this.syncTaskListToGoogle(taskListId, userId, forceUpdate);
            
            // Nếu có googleTaskListId, đồng bộ về database để đảm bảo dữ liệu mới nhất
            if (googleTaskList && googleTaskList.id) {
                await this.syncTaskListFromGoogle(googleTaskList.id, taskList.board_id, userId);
            }

            return googleTaskList;
        } catch (error) {
            console.error('Lỗi khi đồng bộ hai chiều tasklist:', error);
            throw error;
        }
    }

    static async syncTaskToGoogle(taskId, userId, forceUpdate = false) {
        try {
            //lấy thông tin task và tasklist
            const task = await Task.findByPk(taskId, {
                include: [{ model: TaskList, as: 'taskList' }]
            });
            if (!task) throw new Error('Task không tồn tại');

            //nếu tasklist chưa được đồng bộ thì đồng bộ tasklist trước
            if (!task.taskList.googleTaskListId) {
                await this.syncTaskListToGoogle(task.tasklist_id, userId, forceUpdate);
                await task.taskList.reload();
            }

            //lấy thông tin google task list
            const googleTasks = await getAuthenticatedClient(userId);
            let googleTask;

            //tạo request body
            const requestBody = {
                id: task.googleTaskId,
                title: task.title,
                notes: task.notes,
                due: formatDateForGoogleTasks(task.due),
                status: task.status,
                position: task.position,
                completed: formatDateForGoogleTasks(task.completed)
            };

            let parentTaskId = null;

            //nếu task có parent task thì đồng bộ parent task trước
            if (task.parent_task_id) {
                const parentTask = await Task.findByPk(task.parent_task_id);
                if (parentTask) {
                    // Đảm bảo parent task đã được đồng bộ lên Google Tasks
                    if (!parentTask.googleTaskId) {
                        //console.log('>> Parent task chưa được đồng bộ, đồng bộ parent task trước:', parentTask.id);
                        await this.syncTaskToGoogle(parentTask.id, userId, forceUpdate);
                        // Reload parent task để lấy googleTaskId mới được cập nhật
                        await parentTask.reload();
                    }
                    
                    // Lưu googleTaskId của parent để sử dụng trong API call
                    if (parentTask.googleTaskId) {
                        parentTaskId = parentTask.googleTaskId;
                    } 
                }
            }

            if (task.googleTaskId) {
                console.log('>> update task:', task.googleTaskId);
                try {
                    googleTask = await retryGoogleCall(
                        () => googleTasks.tasks.update({
                            tasklist: task.taskList.googleTaskListId,
                            task: task.googleTaskId,
                            requestBody: requestBody
                        }),
                        `update task "${task.title}"`,
                        { taskId: task.id, title: task.title, userId }
                    );
                    if (parentTaskId) {
                        await retryGoogleCall(
                            () => googleTasks.tasks.move({
                                tasklist: task.taskList.googleTaskListId,
                                task: task.googleTaskId,
                                parent: parentTaskId
                            }),
                            `move task "${task.title}" under parent`,
                            { taskId: task.id, parentTaskId, userId }
                        );
                    }
                    return googleTask.data;
                } catch (err) {
                    if (err?.response?.status === 404) {
                        console.warn(`Không tìm thấy task trên Google: "${task.title}" (${task.googleTaskId}) → Xóa local task.`);
                        await Task.destroy({ where: { id: task.id } });
                        return null;
                    } else {
                        throw err;
                    }
                }

            } else {
                console.log('>> insert task:', task.title);
                const insertParams = {
                    tasklist: task.taskList.googleTaskListId,
                    requestBody
                };
                
                if (parentTaskId) {
                    insertParams.parent = parentTaskId;
                }
                
                googleTask = await retryGoogleCall(
                    () => googleTasks.tasks.insert(insertParams),
                    `insert task "${task.title}"`,
                    { taskId: task.id, requestBody, parentTaskId, userId }
                );
                await Task.update(
                    { googleTaskId: googleTask.data.id },
                    { where: { id: taskId } }
                );
            }

            return googleTask.data;
        } catch (error) {
            console.error('Lỗi khi đồng bộ task:', error);
            throw error;
        }
    }

    static async syncAllTasksInList(taskListId, userId, forceUpdate = false) {
        try {
            const tasks = await Task.findAll({
                where: { tasklist_id: taskListId },
                order: [[sequelize.literal('CASE WHEN parent_task_id IS NULL THEN 0 ELSE 1 END ASC')]]
            });

            const results = await Promise.all(
                tasks.map(task => this.syncTaskToGoogle(task.id, userId, forceUpdate))
            );

            return results;
        } catch (error) {
            console.error('Lỗi khi đồng bộ tất cả tasks:', error);
            throw error;
        }
    }

    // Đồng bộ một task từ Google Tasks về database
    static async syncTaskFromGoogle(googleTaskId, taskListId, userId) {
        try {
            const taskList = await TaskList.findByPk(taskListId);
            if (!taskList || !taskList.googleTaskListId) {
                throw new Error('TaskList không tồn tại hoặc chưa được đồng bộ với Google Tasks');
            }

            const googleTasks = await getAuthenticatedClient(userId);
            
            // Lấy thông tin task từ Google Tasks
            const googleTask = await retryGoogleCall(
                () => googleTasks.tasks.get({
                    tasklist: taskList.googleTaskListId,
                    task: googleTaskId
                }),
                `get task from Google`,
                { googleTaskId, taskListId, userId }
            );

            // Kiểm tra xem task đã tồn tại trong database chưa
            let task = await Task.findOne({
                where: { googleTaskId: googleTaskId }
            });

            const taskData = {
                title: googleTask.data.title,
                notes: googleTask.data.notes,
                status: googleTask.data.status,
                position: googleTask.data.position,
                tasklist_id: taskListId,
                googleTaskId: googleTaskId,
                updatedAt: new Date()
            };

            // Xử lý due date
            if (googleTask.data.due) {
                taskData.due = new Date(googleTask.data.due);
            }

            // Xử lý completed date
            if (googleTask.data.completed) {
                taskData.completed = new Date(googleTask.data.completed);
            }

            // Xử lý parent task
            if (googleTask.data.parent) {
                const parentTask = await Task.findOne({
                    where: { googleTaskId: googleTask.data.parent }
                });
                if (parentTask) {
                    taskData.parent_task_id = parentTask.id;
                    console.log(`Task "${googleTask.data.title}" có parent task: "${parentTask.title}"`);
                } else {
                    console.warn(`Không tìm thấy parent task với googleTaskId: ${googleTask.data.parent} cho task: ${googleTask.data.title}`);
                }
            }

            if (task) {
                // Cập nhật task hiện có
                await task.update(taskData);
                console.log(`Cập nhật task: "${googleTask.data.title}" (ID: ${task.id})`);
            } else {
                // Tạo task mới
                taskData.id = uuidv4();
                task = await Task.create(taskData);
                console.log(`Tạo mới task: "${googleTask.data.title}" (ID: ${task.id})`);
            }

            return task;
        } catch (error) {
            console.error(`Lỗi khi đồng bộ task ${googleTaskId} từ Google:`, error);
            throw error;
        }
    }

    // Đồng bộ tất cả tasks từ Google Tasks về database
    static async syncAllTasksFromGoogle(taskListId, userId) {
        try {
            const taskList = await TaskList.findByPk(taskListId);
            if (!taskList || !taskList.googleTaskListId) {
                throw new Error('TaskList không tồn tại hoặc chưa được đồng bộ với Google Tasks');
            }

            const googleTasks = await getAuthenticatedClient(userId);
            
            // Lấy tất cả tasks từ Google Tasks
            const googleTasksList = await retryGoogleCall(
                () => googleTasks.tasks.list({
                    tasklist: taskList.googleTaskListId,
                    showCompleted: true,
                    showHidden: true
                }),
                `list all tasks from Google TaskList`,
                { taskListId, userId }
            );

            if (!googleTasksList.data.items || googleTasksList.data.items.length === 0) {
                console.log(`Không có tasks nào trong tasklist: ${taskList.title}`);
                return [];
            }

            console.log(`Tìm thấy ${googleTasksList.data.items.length} tasks trong tasklist: ${taskList.title}`);

            // Sắp xếp tasks theo thứ tự: parent tasks trước, sau đó đến child tasks
            const tasksToSync = [...googleTasksList.data.items];
            const syncedTasks = [];
            const syncedTaskIds = new Set();

            // Phân loại tasks thành parent và child
            const parentTasks = tasksToSync.filter(task => !task.parent);
            const childTasks = tasksToSync.filter(task => task.parent);

            console.log(`Tìm thấy ${parentTasks.length} parent tasks và ${childTasks.length} child tasks`);

            // Đồng bộ parent tasks trước
            console.log('Đang đồng bộ parent tasks...');
            for (const googleTask of parentTasks) {
                try {
                    const task = await this.syncTaskFromGoogle(googleTask.id, taskListId, userId);
                    if (task) {
                        syncedTasks.push(task);
                        syncedTaskIds.add(googleTask.id);
                    }
                } catch (error) {
                    console.error(`Lỗi khi đồng bộ parent task ${googleTask.id}:`, error);
                }
            }

            // Sau đó đồng bộ child tasks
            console.log('Đang đồng bộ child tasks...');
            for (const googleTask of childTasks) {
                if (!syncedTaskIds.has(googleTask.id)) {
                    try {
                        const task = await this.syncTaskFromGoogle(googleTask.id, taskListId, userId);
                        if (task) {
                            syncedTasks.push(task);
                            syncedTaskIds.add(googleTask.id);
                        }
                    } catch (error) {
                        console.error(`Lỗi khi đồng bộ child task ${googleTask.id}:`, error);
                    }
                }
            }

            console.log(`Đã đồng bộ thành công ${syncedTasks.length}/${tasksToSync.length} tasks cho tasklist: ${taskList.title}`);
            return syncedTasks;
        } catch (error) {
            console.error('Lỗi khi đồng bộ tất cả tasks từ Google:', error);
            throw error;
        }
    }

    static async syncEntireBoard(boardId, userId, forceUpdate = false) {
        try {
            const board = await Board.findByPk(boardId);
            if (!board) throw new Error('Board không tồn tại');

            console.log(`Bắt đầu đồng bộ board "${board.name}" (ID: ${boardId})`);

            // Bước 1: Đồng bộ tasklist từ Google Tasks về database
            console.log('Bước 1: Đồng bộ tasklist từ Google Tasks về database...');
            const syncedTaskLists = await this.syncAllTaskListsFromGoogle(boardId, userId);
            console.log(`Đã đồng bộ ${syncedTaskLists.length} tasklist từ Google Tasks`);

            // Bước 2: Đồng bộ tasks từ Google Tasks về database cho mỗi tasklist
            console.log('Bước 2: Đồng bộ tasks từ Google Tasks về database...');
            const allTasksResults = [];
            for (const taskList of syncedTaskLists) {
                try {
                    console.log(`Đang đồng bộ tasks cho tasklist: ${taskList.title}`);
                    const tasks = await this.syncAllTasksFromGoogle(taskList.id, userId);
                    allTasksResults.push({
                        taskListId: taskList.id,
                        taskListTitle: taskList.title,
                        tasks: tasks
                    });
                    console.log(`Đã đồng bộ ${tasks.length} tasks cho tasklist: ${taskList.title}`);
                } catch (error) {
                    console.error(`Lỗi khi đồng bộ tasks cho tasklist ${taskList.id}:`, error);
                }
            }

            // Bước 3: Đồng bộ tasklist từ database lên Google Tasks (để đảm bảo dữ liệu mới nhất)
            console.log('Bước 3: Đồng bộ tasklist từ database lên Google Tasks...');
            const taskLists = await TaskList.findAll({ where: { board_id: boardId } });
            const syncedTaskListsToGoogle = await Promise.all(
                taskLists.map(async (taskList) => {
                    const syncedTaskList = await this.syncTaskListBidirectional(taskList.id, userId, forceUpdate);
                    return syncedTaskList;
                })
            );

            // Bước 4: Đồng bộ tasks từ database lên Google Tasks
            console.log('Bước 4: Đồng bộ tasks từ database lên Google Tasks...');
            const syncedTasksToGoogle = await Promise.all(
                taskLists.map(async (taskList) => {
                    const syncedTasks = await this.syncAllTasksInList(taskList.id, userId, forceUpdate);
                    return {
                        taskListId: taskList.id,
                        taskListTitle: taskList.title,
                        tasks: syncedTasks
                    };
                })
            );

            await Board.update(
                { isGoogleSynced: true },
                { where: { id: boardId } }
            );

            console.log(`Hoàn thành đồng bộ board "${board.name}"`);

            return {
                board: board,
                taskListsFromGoogle: allTasksResults,
                taskListsToGoogle: syncedTaskListsToGoogle,
                tasksToGoogle: syncedTasksToGoogle
            };
        } catch (error) {
            console.error('Lỗi khi đồng bộ toàn bộ board:', error);
            throw error;
        }
    }

    // Đồng bộ hoàn toàn từ Google Tasks về database
    static async syncEntireBoardFromGoogle(boardId, userId) {
        try {
            const board = await Board.findByPk(boardId);
            if (!board) throw new Error('Board không tồn tại');

            console.log(`Bắt đầu đồng bộ board "${board.name}" từ Google Tasks`);

            // Đồng bộ tất cả tasklist từ Google Tasks
            const syncedTaskLists = await this.syncAllTaskListsFromGoogle(boardId, userId);
            
            console.log(`Đã đồng bộ ${syncedTaskLists.length} tasklist từ Google Tasks`);

            // Đồng bộ tất cả tasks trong mỗi tasklist
            const allTasksResults = [];
            for (const taskList of syncedTaskLists) {
                try {
                    const tasks = await this.syncAllTasksFromGoogle(taskList.id, userId);
                    allTasksResults.push({
                        taskListId: taskList.id,
                        taskListTitle: taskList.title,
                        tasks: tasks
                    });
                } catch (error) {
                    console.error(`Lỗi khi đồng bộ tasks cho tasklist ${taskList.id}:`, error);
                }
            }

            await Board.update(
                { isGoogleSynced: true },
                { where: { id: boardId } }
            );

            return {
                board: board,
                taskLists: syncedTaskLists,
                tasks: allTasksResults
            };
        } catch (error) {
            console.error('Lỗi khi đồng bộ board từ Google Tasks:', error);
            throw error;
        }
    }
    
    // Sửa chữa parent-child relationships cho tasks
    static async fixTaskParentChildRelationships(taskListId) {
        try {
            console.log(`Bắt đầu sửa chữa parent-child relationships cho tasklist: ${taskListId}`);
            
            const tasks = await Task.findAll({
                where: { tasklist_id: taskListId },
                include: [{ model: Task, as: 'parentTask' }]
            });

            let fixedCount = 0;
            let errorCount = 0;

            for (const task of tasks) {
                try {
                    if (task.parent_task_id) {
                        // Kiểm tra xem parent task có tồn tại không
                        const parentTask = await Task.findByPk(task.parent_task_id);
                        if (!parentTask) {
                            console.warn(`Task "${task.title}" có parent_task_id không tồn tại: ${task.parent_task_id}`);
                            // Xóa parent_task_id không hợp lệ
                            await task.update({ parent_task_id: null });
                            fixedCount++;
                        }
                    }
                } catch (error) {
                    console.error(`Lỗi khi sửa chữa task ${task.id}:`, error);
                    errorCount++;
                }
            }

            console.log(`Hoàn thành sửa chữa: ${fixedCount} tasks được sửa, ${errorCount} lỗi`);
            return { fixedCount, errorCount };
        } catch (error) {
            console.error('Lỗi khi sửa chữa parent-child relationships:', error);
            throw error;
        }
    }

    // Đồng bộ hoàn chỉnh với sửa chữa
    static async syncEntireBoardWithFix(boardId, userId, forceUpdate = false) {
        try {
            console.log(`Bắt đầu đồng bộ hoàn chỉnh board ${boardId} với sửa chữa...`);
            
            // Thực hiện đồng bộ bình thường
            const syncResult = await this.syncEntireBoard(boardId, userId, forceUpdate);
            
            // Sửa chữa parent-child relationships cho tất cả tasklists
            const taskLists = await TaskList.findAll({ where: { board_id: boardId } });
            const fixResults = [];
            
            for (const taskList of taskLists) {
                try {
                    const fixResult = await this.fixTaskParentChildRelationships(taskList.id);
                    fixResults.push({
                        taskListId: taskList.id,
                        taskListTitle: taskList.title,
                        ...fixResult
                    });
                } catch (error) {
                    console.error(`Lỗi khi sửa chữa tasklist ${taskList.id}:`, error);
                }
            }
            
            return {
                ...syncResult,
                fixResults
            };
        } catch (error) {
            console.error('Lỗi khi đồng bộ hoàn chỉnh với sửa chữa:', error);
            throw error;
        }
    }


}

module.exports = AsyncService;