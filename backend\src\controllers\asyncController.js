const AsyncService = require('../services/asyncService');
const cron = require('node-cron');


// Thiết lập cron job để chạy mỗi 1 phút
// cron.schedule('* * * * *', async () => {
//     try {   
//       await AsyncService.autoSyncData();
//       console.log('Đã hoàn thành tự động đồng bộ với Google Task');
//     } catch (error) {
//       console.error('Lỗi khi tự động đồng bộ với Google Task:', error);
//     }
// });


const syncTaskList = async (req, res) => {
    try {
        const { taskListId } = req.params;
        const result = await AsyncService.syncTaskListToGoogle(taskListId, req.user.id);
        res.json({
            success: true,
            message: 'Đồng bộ task list thành công',
            data: result
        });
    } catch (error) {
        console.error('Lỗi khi đồng bộ task list:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi đồng bộ task list',
            error: error.message
        });
    }
};

const syncTask = async (req, res) => {
    try {
        const { taskId } = req.params;
        const result = await AsyncService.syncTaskToGoogle(taskId, req.user.id);
        res.json({
            success: true,
            message: 'Đồng bộ task thành công',
            data: result
        });
    } catch (error) {
        console.error('Lỗi khi đồng bộ task:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi đồng bộ task',
            error: error.message
        });
    }
};

const syncAllTasksInList = async (req, res) => {
    try {
        const { taskListId } = req.params;
        const result = await AsyncService.syncAllTasksInList(taskListId, req.user.id);
        res.json({
            success: true,
            message: 'Đồng bộ tất cả tasks trong list thành công',
            data: result
        });
    } catch (error) {
        console.error('Lỗi khi đồng bộ tất cả tasks:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi đồng bộ tất cả tasks',
            error: error.message
        });
    }
};

const syncEntireBoard = async (req, res) => {
    try {
        const { boardId } = req.params;
        const result = await AsyncService.syncEntireBoard(boardId, req.user.id);
        res.json({
            success: true,
            message: 'Đồng bộ toàn bộ board thành công',
            data: result
        });
    } catch (error) {
        console.error('Lỗi khi đồng bộ board:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi đồng bộ board',
            error: error.message
        });
    }
};

module.exports = {
    syncTaskList,
    syncTask,
    syncAllTasksInList,
    syncEntireBoard
};
