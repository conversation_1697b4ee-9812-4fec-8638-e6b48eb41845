const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * <PERSON>yển định dạng ngày dạng "2025-07-15 17:07:35" thành chuẩn ISO cho Google Tasks.
 * <PERSON><PERSON><PERSON> về undefined nếu đầu vào null.
 */
function formatDateForGoogleTasks(dateString) {
  if (!dateString) return undefined;

  const isoString = dayjs.tz(dateString, 'Asia/Ho_Chi_Minh').toISOString();
  return isoString;
}

module.exports = { formatDateForGoogleTasks };
