/* Dashboard Layout */
.dashboard-layout {
    height: 100vh;
    background: #f8f9fa;
}

/* Header Styles */
.dashboard-header {
    background: #ffffff;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.collapse-btn {
    font-size: 16px;
    color: #595959;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    color: #1f1f1f;
}

.logo-text {
    background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #fbbc05 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* .header-center {
        flex: 1;
        display: flex;
        justify-content: center; 
    } */

.search-input {
    max-width: 400px;
    border-radius: 24px;
    background: #f5f5f5;
    border: none;
}

.search-input:hover,
.search-input:focus {
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-right .ant-btn {
    color: #595959;
    border: none;
    box-shadow: none;
}

.header-right .ant-avatar {
    border: 2px solid #f0f0f0;
}

.user-avatar {
    transition: all 0.2s ease;
}

.user-avatar:hover {
    transform: scale(1.05);
    border-color: #40a9ff !important;
}

/* Sidebar Styles */
.dashboard-sidebar {
    background: #ffffff;
    border-right: 1px solid #e8e8e8;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
}

.dashboard-sidebar.ant-layout-sider-collapsed {
    width: 0px !important;
    max-width: 0px !important;
    min-width: 0px !important;
    flex: 0 0 0px !important;
}

.dashboard-sidebar.ant-layout-sider-collapsed .sidebar-content {
    display: none;
}

.sidebar-content {
    padding: 24px 0;
}

.sidebar-section {
    margin-bottom: 32px;
    padding: 0 16px;
}

.sidebar-section h3 {
    color: #262626;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 16px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.section-header h3 {
    margin: 0;
}

/* Task Lists Menu */
.task-lists-menu {
    background: transparent;
    border: none;
}

.task-lists-menu .ant-menu-item {
    padding: 8px 12px;
    margin: 4px 0;
    border-radius: 8px;
    height: auto;
    line-height: normal;
}

.task-lists-menu .ant-menu-item:hover {
    background: #f0f7ff;
}

.task-lists-menu .ant-menu-item-selected {
    background: #e6f4ff;
    border: 1px solid #91caff;
}

.menu-item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.list-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.list-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.list-name {
    font-weight: 500;
    color: #262626;
}

.list-count .ant-badge-count {
    background: #f0f0f0;
    color: #595959;
    font-size: 11px;
    min-width: 18px;
    height: 18px;
    line-height: 18px;
}

/* Integration Items */
.integration-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.integration-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.integration-item:hover {
    background: #f5f5f5;
}

.integration-item span {
    font-size: 13px;
    color: #595959;
}

/* Progress Section */
.progress-section {
    background: #fafafa;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
}

.progress-item {
    margin-bottom: 12px;
}

.progress-item span {
    display: block;
    font-size: 12px;
    color: #595959;
    margin-bottom: 8px;
}

.stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #595959;
}

/* Main Content */
.main-layout {
    background: #f8f9fa;
}

.dashboard-content {
    padding: 24px;
    height: calc(100vh - 64px);
    overflow-y: auto;
    overflow-x: hidden;
}

/* Content Header */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    background: #ffffff;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    flex-shrink: 0;
}

.content-title h1 {
    color: #1f1f1f;
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 4px;
    line-height: 1.3;
}

.content-title p {
    color: #8c8c8c;
    font-size: 14px;
    margin: 0;
}

.content-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.content-actions .ant-btn {
    border-radius: 6px;
    font-weight: 500;
    height: 32px;
    padding: 0 12px;
    font-size: 14px;
}

/* Task Board */
.task-board {
    display: flex;
    gap: 24px;
    margin-bottom: 32px;
    overflow-x: auto;
    overflow-y: visible;
    padding-bottom: 16px;
    scrollbar-width: thin;
    scrollbar-color: #d9d9d9 transparent;
    flex: 1;
    min-height: 0;
}

.task-board::-webkit-scrollbar {
    height: 8px;
}

.task-board::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 4px;
}

.task-board::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 4px;
}

.task-board::-webkit-scrollbar-thumb:hover {
    background: #bfbfbf;
}

.board-column {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    flex-shrink: 0;
    width: 320px;
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 200px);
}

.column-header {
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fafafa;
    position: relative;
}

.column-title {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 0;
    flex: 1;
}

.column-title h3 {
    color: #262626;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.column-title .list-color {
    flex-shrink: 0;
}

.column-content {
    padding: 16px;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
    max-height: calc(100vh - 300px);
}

/* Task Cards */
.task-card {
    margin-bottom: 8px;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.task-card:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.task-card .task-header {
    display: flex;
    align-items: center;
    position: relative;
}

.task-card .task-title {
    flex: 1;
    margin: 0;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    display: flex;
    align-items: center;
}

/* Task cha có border đặc biệt khi có task con */
.task-card.has-children {
    border-left: 3px solid #1890ff;
}

/* Task con có style riêng */
.task-card.subtask-card {
    border-left: 3px solid #1890ff;
    background-color: #fafafa;
    margin-left: 20px;
}

/* Task đã hoàn thành */
.task-card.completed-task {
    background-color: #f5f5f5;
    opacity: 0.7;
}

/* Khi kéo task */
.task-card.dragging {
    opacity: 0.7;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

/* Hiệu ứng khi kéo task có task con */
.task-card.dragging.has-children + .subtask-card.dragging {
    margin-top: 0;
}

/* Task priority indicator */
.task-priority {
    width: 4px;
    height: 16px;
    margin-right: 8px;
    border-radius: 2px;
}

.task-title {
    color: #262626;
    font-size: 14px;
    font-weight: 500;
    margin: 0;
    line-height: 1.4;
    flex: 1;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 2px 4px;
    margin: -2px -4px;
}

.task-title.editable:hover {
    background: rgba(24, 144, 255, 0.1);
    color: #1890ff;
    transform: translateY(-1px);
}

.task-menu-btn {
    opacity: 0;
    transition: opacity 0.2s ease;
    color: #8c8c8c;
}

.task-card:hover .task-menu-btn {
    opacity: 1;
}

/* Editable Elements */
.editable {
    cursor: pointer;
    transition: all 0.2s ease;
}

.editable:hover {
    background: rgba(24, 144, 255, 0.1);
    border-radius: 4px;
    padding: 2px 6px;
    margin: -2px -6px;
}

.task-title-input {
    border: none;
    box-shadow: 0 0 0 2px #40a9ff;
    border-radius: 6px;
    background: #ffffff;
    font-size: 14px;
    font-weight: 500;
    margin: -2px -4px;
    padding: 4px 8px;
}

.task-title-input:focus {
    border: none;
    box-shadow: 0 0 0 2px #40a9ff, 0 4px 12px rgba(24, 144, 255, 0.2);
    transform: translateY(-1px);
}

/* Board name input and List name input */
.board-name-input,
.list-name-input {
    border: none;
    box-shadow: 0 0 0 2px #40a9ff;
    border-radius: 4px;
    background: #ffffff;
    font-size: 14px;
    font-weight: 500;
}

.board-name-input:focus,
.list-name-input:focus {
    border: none;
    box-shadow: 0 0 0 2px #40a9ff;
}

/* Override for larger list names */
.list-name-input {
    font-size: 16px;
    font-weight: 600;
}

/* List Title Section */
.list-title-section {
    flex: 1;
    min-width: 0; /* Allows text to truncate */
}

.list-description {
    cursor: pointer;
    transition: all 0.2s ease;
    word-wrap: break-word;
    overflow-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
}

.list-description:hover {
    background: rgba(24, 144, 255, 0.1);
    border-radius: 4px;
    padding: 2px 6px;
    margin: -2px -6px;
}

.list-description-input {
    border: none;
    box-shadow: 0 0 0 2px #40a9ff;
    border-radius: 4px;
    background: #ffffff;
    font-size: 12px;
    margin-top: 4px;
}

.list-description-input:focus {
    border: none;
    box-shadow: 0 0 0 2px #40a9ff;
}

.task-desc-input {
    border: none;
    box-shadow: 0 0 0 2px #40a9ff;
    border-radius: 6px;
    background: #ffffff;
    resize: none;
    font-size: 13px;
    line-height: 1.4;
    margin: 4px -6px 8px -6px;
    padding: 8px 10px;
}

.task-desc-input:focus {
    border: none;
    box-shadow: 0 0 0 2px #40a9ff, 0 4px 12px rgba(24, 144, 255, 0.2);
    transform: translateY(-1px);
}

.task-desc-input::placeholder {
    color: #bfbfbf;
    font-style: italic;
}

.task-date-picker {
    border: none;
    box-shadow: 0 0 0 2px #40a9ff;
    border-radius: 6px;
    background: #ffffff;
    margin: -3px -6px;
}

.task-date-picker:focus {
    border: none;
    box-shadow: 0 0 0 2px #40a9ff, 0 4px 12px rgba(24, 144, 255, 0.2);
    transform: translateY(-1px);
}

.task-date-picker .ant-picker-input > input {
    font-size: 12px;
    padding: 4px 8px;
}

/* Task Description */
.task-description {
    margin-bottom: 12px;
}

.task-desc {
    color: #595959;
    font-size: 13px;
    line-height: 1.4;
    margin: 0;
    margin-bottom: 8px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 4px 6px;
    margin: 4px -6px 8px -6px;
}

.task-desc.editable:hover {
    background: rgba(24, 144, 255, 0.1);
    color: #1890ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
}

.task-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #8c8c8c;
}

.task-assignee {
    display: flex;
    align-items: center;
    gap: 6px;
}

.task-assignee span {
    font-weight: 500;
}

/* Styles cho AssigneeSelector */
.task-assignee .ant-btn-text {
    padding: 0 4px;
    height: auto;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.task-assignee .ant-btn-text:hover {
    background: rgba(24, 144, 255, 0.1);
    color: #1890ff;
}

.task-assignee .ant-avatar {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.task-assignee .ant-avatar:hover {
    transform: scale(1.1);
    border-color: #ff4d4f;
    box-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
}

/* Styles cho assignee avatar container */
.assignee-avatar-container {
    position: relative;
    display: inline-block;
}

.assignee-avatar-container:hover .remove-assignee-btn {
    display: flex !important;
}

.assignee-avatar-container:hover .ant-avatar {
    border-color: #40a9ff !important;
}

.remove-assignee-btn:hover {
    transform: scale(1.1);
}

.task-due-date {
    display: flex;
    align-items: center;
    gap: 4px;
}

.date-text {
    font-size: 12px;
    color: #8c8c8c;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 3px 6px;
    margin: -3px -6px;
}

.date-text.editable:hover {
    background: rgba(24, 144, 255, 0.1);
    color: #1890ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
}

/* Integration Banner */
.integration-banner {
    background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
    border-radius: 16px;
    padding: 48px;
    color: #ffffff;
    text-align: center;
    box-shadow: 0 8px 24px rgba(66, 133, 244, 0.3);
}

.banner-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    max-width: 600px;
    margin: 0 auto;
}

.banner-text h2 {
    color: #ffffff;
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 8px;
}

.banner-text p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    line-height: 1.6;
    margin: 0;
}

.integration-banner .ant-btn-primary {
    background: #ffffff;
    color: #4285f4;
    border: none;
    font-weight: 600;
    height: 48px;
    padding: 0 32px;
    border-radius: 24px;
    font-size: 16px;
}

.integration-banner .ant-btn-primary:hover {
    background: #f0f0f0;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Desktop and Large Screens - Optimized layout */
@media (min-width: 769px) {
    .dashboard-content {
        height: calc(100vh - 64px);
        overflow-y: auto;
        overflow-x: hidden;
        display: flex;
        flex-direction: column;
    }

    .task-board {
        flex: 1;
        overflow-x: auto;
        overflow-y: visible;
        min-height: 0;
        display: flex;
    }

    .board-column {
        max-height: calc(100vh - 200px);
        display: flex;
        flex-direction: column;
    }

    .column-content {
        flex: 1;
        overflow-y: auto;
        min-height: 0;
        max-height: none;
    }
}

/* Mobile First Responsive Design */
@media (max-width: 1200px) {
    .dashboard-sidebar {
        width: 260px;
    }

    .search-input {
        max-width: 300px;
    }

    .task-board {
        gap: 16px;
    }

    .board-column {
        min-width: 300px;
    }
}

@media (max-width: 992px) {
    .dashboard-sidebar {
        width: 240px;
    }

    .search-input {
        max-width: 250px;
    }

    .content-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }

    .content-actions {
        width: 100%;
        justify-content: flex-start;
        overflow-x: auto;
    }

    .task-board {
        gap: 12px;
    }

    .board-column {
        min-width: 280px;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 0 12px;
    }

    .header-center {
        display: none;
    }

    .header-right {
        gap: 8px;
    }

    .header-right .ant-btn {
        padding: 4px 8px;
    }

    .dashboard-content {
        padding: 16px;
        min-height: calc(100vh - 56px);
        height: auto;
        overflow-y: auto;
    }

    .content-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
        padding: 12px 16px;
        margin-bottom: 12px;
    }

    .content-actions {
        flex-wrap: wrap;
        gap: 8px;
    }

    .content-actions .ant-btn {
        flex: 1;
        min-width: 120px;
    }

    .task-board {
        gap: 12px;
        overflow-x: auto;
        overflow-y: visible;
        flex-direction: row;
        height: auto;
        min-height: auto;
        padding-bottom: 24px;
    }

    .board-column,
    .add-tasklist-column {
        min-width: 280px;
        flex-shrink: 0;
    }

    .column-content {
        max-height: calc(100vh - 200px);
    }

    .integration-banner {
        margin: 12px;
        padding: 16px;
    }

    .banner-text h2 {
        font-size: 20px;
    }

    .banner-content {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    /* Mobile Sidebar */
    .dashboard-sidebar {
        position: fixed !important;
        left: 0;
        top: 70px;
        height: calc(100vh - 70px);
        z-index: 1001;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        width: 280px !important;
        max-width: 280px !important;
        min-width: 280px !important;
        flex: 0 0 280px !important;
    }

    .dashboard-sidebar.ant-layout-sider-collapsed {
        transform: translateX(-100%);
        width: 280px !important;
        max-width: 280px !important;
        min-width: 280px !important;
        flex: 0 0 280px !important;
    }

    .dashboard-sidebar:not(.ant-layout-sider-collapsed) {
        transform: translateX(0);
    }

    .dashboard-sidebar.ant-layout-sider-collapsed .sidebar-content {
        display: block;
    }

    /* Sidebar Overlay */
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    /* Main content margin adjustment for mobile */
    .main-layout {
        margin-left: 0 !important;
    }
}

@media (max-width: 576px) {
    .dashboard-header {
        padding: 0 8px;
    }

    .header-left {
        gap: 8px;
    }

    .logo {
        font-size: 16px;
    }

    .header-right {
        gap: 4px;
    }

    .dashboard-content {
        padding: 12px;
        min-height: calc(100vh - 56px);
        height: auto;
    }

    .content-header {
        padding: 10px 12px;
        margin-bottom: 8px;
    }

    .content-title h1 {
        font-size: 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
    }

    .content-title p {
        font-size: 14px;
    }

    .content-actions .ant-btn {
        min-width: 100px;
        font-size: 12px;
        padding: 4px 8px;
        height: 28px;
    }

    .task-board {
        gap: 8px;
        height: auto;
        min-height: auto;
        padding-bottom: 32px;
    }

    .board-column,
    .add-tasklist-column {
        min-width: 260px;
    }

    .column-content {
        max-height: calc(100vh - 180px);
        min-height: 300px;
    }

    .task-card {
        margin-bottom: 8px;
    }

    .integration-banner {
        margin: 8px;
        padding: 12px;
    }

    .banner-text h2 {
        font-size: 18px;
    }

    .banner-text p {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .header-left .logo-text {
        display: none;
    }

    .dashboard-content {
        padding: 8px;
        min-height: calc(100vh - 56px);
        height: auto;
    }

    .content-actions {
        flex-direction: column;
    }

    .content-actions .ant-btn {
        width: 100%;
        min-width: unset;
    }

    .task-board {
        height: auto;
        min-height: auto;
        padding-bottom: 40px;
    }

    .board-column,
    .add-tasklist-column {
        min-width: 240px;
    }

    .column-header {
        padding: 12px;
    }

    .column-content {
        max-height: calc(100vh - 160px);
        min-height: 250px;
    }

    .task-card {
        padding: 12px;
    }

    .integration-banner {
        margin: 4px;
        padding: 8px;
    }
}

/* Extra small devices (very small phones) */
@media (max-width: 380px) {
    .dashboard-content {
        padding: 4px;
    }

    .content-header {
        padding: 8px 10px;
        margin-bottom: 6px;
    }

    .content-title h1 {
        font-size: 18px;
    }

    .task-board {
        gap: 4px;
        height: auto;
        padding-bottom: 20px;
    }

    .board-column,
    .add-tasklist-column {
        min-width: 220px;
    }

    .column-header {
        padding: 8px;
    }

    .column-content {
        padding: 8px;
        max-height: calc(100vh - 140px);
        min-height: 200px;
    }

    .content-actions .ant-btn {
        font-size: 11px;
        padding: 2px 6px;
    }
}

/* Scrollbar Styling */
.column-content::-webkit-scrollbar,
.dashboard-content::-webkit-scrollbar {
    width: 6px;
}

.column-content::-webkit-scrollbar-track,
.dashboard-content::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 3px;
}

.column-content::-webkit-scrollbar-thumb,
.dashboard-content::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 3px;
}

.column-content::-webkit-scrollbar-thumb:hover,
.dashboard-content::-webkit-scrollbar-thumb:hover {
    background: #bfbfbf;
}

/* Utility Classes */
.mobile-hidden {
    display: block;
}

.mobile-visible {
    display: none;
}

/* Responsive overflow fixes */
.responsive-container {
    overflow-x: hidden;
    width: 100%;
}

.responsive-content {
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

/* Scrollable container for small screens */
.scrollable-horizontal {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
}

@media (max-width: 768px) {
    .mobile-hidden {
        display: none !important;
    }

    .mobile-visible {
        display: block !important;
    }

    /* Ensure content doesn't get cut off */
    body {
        overflow-x: hidden;
    }

    /* Better scroll behavior on mobile */
    .task-board {
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch;
    }

    .board-column {
        scroll-snap-align: start;
    }
}

/* Animation */
.task-card,
.board-column,
.content-header,
.integration-banner {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Add Task Card */
.add-task-card {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: pointer;

    background: #fafafa;
}

.add-task-card:hover {
    border-color: #40a9ff;
    background: #f0f7ff;
}

.add-task-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px;
    color: #8c8c8c;
}

.add-task-text {
    font-size: 13px;
    font-weight: 500;
}

.add-task-card:hover .add-task-content {
    color: #1890ff;
}

/* Add TaskList Card */
.add-tasklist-column {
    flex-shrink: 0;
    width: 320px;
}

.add-tasklist-card {
    border: 2px dashed #d9d9d9;
    border-radius: 12px;
    transition: all 0.2s ease;
    cursor: pointer;
    background: #fafafa;
    /* height: 200px; */
    display: flex;
    align-items: center;
    justify-content: center;
}

.add-tasklist-card:hover {
    border-color: #40a9ff;
    background: #f0f7ff;
}

.add-tasklist-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;
    padding: 32px;
    color: #8c8c8c;
    text-align: center;
}

.add-tasklist-text {
    font-size: 16px;
    font-weight: 500;
}

.add-tasklist-card:hover .add-tasklist-content {
    color: #1890ff;
}

/* List Menu Button */
.list-menu-btn,
.board-menu-btn {
    opacity: 0;
    transition: opacity 0.2s ease;
    color: #8c8c8c;
}

.board-column:hover .list-menu-btn {
    opacity: 1;
}

.menu-item-content:hover .board-menu-btn,
.board-menu-item:hover .board-menu-btn {
    opacity: 1;
}

/* Board Menu Item - Custom styling to replace Menu.Item */
.board-menu-item {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 4px;
}

.board-item-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: rgba(255, 255, 255, 0.85);
}

.board-item-content:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.board-item-content.selected {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
    border-left: 3px solid #ffffff;
}

.board-menu-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
}

/* Dropdown Items */
.dropdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;
    color: #262626;
}

.dropdown-item .anticon {
    font-size: 14px;
}

.delete-item {
    color: #ff4d4f;
}

.delete-item:hover {
    background: #fff2f0;
}

/* User Dropdown Menu */
.user-dropdown-menu {
    min-width: 220px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #f0f0f0;
}

.user-dropdown-menu .ant-dropdown-menu-item {
    padding: 12px 16px;
}

.user-info {
    padding: 4px 0;
}

.user-name {
    font-weight: 600;
    color: #262626;
    font-size: 14px;
    margin-bottom: 2px;
}

.user-email {
    font-size: 12px;
    color: #8c8c8c;
}

.user-dropdown-menu .ant-dropdown-menu-item-disabled {
    cursor: default;
}

.user-dropdown-menu .ant-dropdown-menu-item-disabled:hover {
    background: transparent;
}

.logout-item {
    color: #ff4d4f !important;
}

.logout-item:hover {
    background: #fff2f0 !important;
}

.user-dropdown-menu .ant-dropdown-menu-item:hover {
    background: #f5f5f5;
}

/* Logout Loading Overlay */
.logout-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

.logout-spinner {
    background: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    min-width: 200px;
}

.logout-spinner p {
    margin-top: 16px;
    font-size: 16px;
    color: #666;
    font-weight: 500;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1100;
}

.loading-spinner {
    background: white;
    padding: 24px 32px;
    border-radius: 12px;
    font-weight: 600;
    color: #374151;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    position: relative;
}

.loading-spinner::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Task con Styles - Google Tasks inspired (chỉ 1 cấp) */
.subtask-card {
    margin-left: 0 !important; /* Override the manual margin-left */
    background: linear-gradient(145deg, #fafafa 0%, #f5f5f5 100%) !important;
    border-left: 4px solid #1890ff !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.08) !important;
    position: relative;
    overflow: hidden;
}

.subtask-card:hover {
    background: linear-gradient(145deg, #f0f7ff 0%, #e6f4ff 100%) !important;
    border-left-color: #40a9ff !important;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15) !important;
    transform: translateY(-1px);
}

.subtask-title {
    font-size: 13px !important;
    font-weight: 500 !important;
    color: #595959 !important;
}

/* Visual hierarchy chỉ 1 cấp: task cha và task con */
.task-card[data-level='1'] {
    margin-left: 20px;
}

/* Enhanced animation cho subtask */
.subtask-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: left center;
}

/* Micro-interactions cho subtask */
.subtask-card:hover {
    animation: subtaskPulse 0.6s ease-in-out;
}

@keyframes subtaskPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
    100% {
        transform: scale(1);
    }
}

/* Hiệu ứng khi task con được tạo mới */
.subtask-card.newly-created {
    animation: slideInFromLeft 0.5s ease-out;
}

@keyframes slideInFromLeft {
    from {
        transform: translateX(-100px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Hiệu ứng cho task title của subtask */
.subtask-title {
    transition: all 0.3s ease;
}

.subtask-card:hover .subtask-title {
    color: #262626 !important;
    transform: translateX(2px);
}

/* Task con với priority indicator nhỏ hơn */
.subtask-card .task-priority {
    width: 8px !important;
    height: 8px !important;
    border-radius: 50% !important;
    margin-right: 6px !important;
}

/* Thêm subtle pattern cho subtask */
.subtask-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(24, 144, 255, 0.1) 50%,
        transparent 100%
    );
}

/* Spacing adjustments cho subtask content */
.subtask-card .task-description {
    margin-top: 6px;
}

.subtask-card .task-desc {
    font-size: 12px;
    line-height: 1.3;
    font-style: italic;
    color: #8c8c8c !important;
}

.subtask-card .task-details {
    margin-top: 8px;
    opacity: 0.9;
}

/* Task cha với visual enhancement */
.task-card:not(.subtask-card) {
    border: 1px solid #f0f0f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.task-card:not(.subtask-card):hover {
    border-color: #d9d9d9;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

/* Indicator cho task cha có con */
.task-card.has-children {
    border-left: 3px solid #52c41a;
    background: linear-gradient(to right, #f6ffed 0%, #ffffff 10%);
}

.task-card.has-children:hover {
    border-left-color: #389e0d;
    background: linear-gradient(to right, #f0f9ff 0%, #ffffff 10%);
}

/* Completed Task Styles */
.completed-task {
    transition: all 0.3s ease;
}

.completed-task .task-card {
    background-color: #f5f5f5 !important;
    opacity: 0.7;
    border: 1px solid #e8e8e8;
}

.completed-task .task-title {
    text-decoration: line-through;
    color: #8c8c8c !important;
}

.completed-task .task-desc {
    text-decoration: line-through;
    color: #a6a6a6 !important;
}

.completed-task .task-assignee span,
.completed-task .date-text {
    color: #8c8c8c !important;
}

.completed-task .ant-avatar {
    opacity: 0.6;
}

.completed-task .anticon {
    color: #8c8c8c !important;
}

/* Checkbox styling trong TaskCard */
.task-header .ant-checkbox-wrapper {
    margin-right: 8px;
    visibility: hidden;
    transition: all 0.2s ease;
    width: 0;
    margin-right: 0;
    overflow: hidden;
}

/* Hiện checkbox khi hover vào task card */
.task-card:hover .task-header .ant-checkbox-wrapper {
    visibility: visible;
    width: auto;
    margin-right: 8px;
}

/* Luôn hiện checkbox nếu task đã completed */
.task-card.completed-task .task-header .ant-checkbox-wrapper {
    visibility: visible;
    width: auto;
    margin-right: 8px;
}

.task-header .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #52c41a;
    border-color: #52c41a;
}

.task-header .ant-checkbox-inner {
    border-radius: 50% !important;
    width: 16px !important;
    height: 16px !important;
}

.task-header .ant-checkbox-checked .ant-checkbox-inner::after {
    border-radius: 50%;
}

/* Hiệu ứng fade khi task được complete */
.task-card {
    transition: all 0.3s ease;
}

.task-card.completed-task {
    transform: translateY(0);
    animation: fadeToCompleted 0.5s ease;
}

@keyframes fadeToCompleted {
    from {
        opacity: 1;
        background-color: #ffffff;
    }
    to {
        opacity: 0.7;
        background-color: #f5f5f5;
    }
}

/* Completed subtask styles */
.subtask-card.completed-task {
    background: linear-gradient(145deg, #f0f0f0 0%, #e8e8e8 100%) !important;
    border-left-color: #bfbfbf !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

.subtask-card.completed-task:hover {
    background: linear-gradient(145deg, #ebebeb 0%, #e0e0e0 100%) !important;
    border-left-color: #a6a6a6 !important;
    transform: translateY(0);
}

/* Completed subtask title giống task cha */
.subtask-card.completed-task .subtask-title {
    color: #999999 !important;
    text-decoration: line-through !important;
}

/* Nút thu gọn/mở rộng task con */
.task-expand-btn {
    color: #666 !important;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.task-expand-btn:hover,
.task-expand-btn:focus {
    color: #1890ff !important;
    background: rgba(24, 144, 255, 0.1) !important;
    transform: scale(1.1);
    border-radius: 4px;
}

.task-expand-btn .anticon {
    font-size: 12px;
    transition: transform 0.2s ease;
}

/* Animation cho việc thu gọn/mở rộng */
@keyframes expandChildren {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.task-children-container {
    animation: expandChildren 0.3s ease;
}

/* Workspace Selector Styles */
.workspace-selector {
    margin-bottom: 16px;
}

.workspace-button {
    width: 100%;
    height: auto !important;
    min-height: 48px;
    padding: 8px 12px !important;
    text-align: left !important;
    border: 1px solid #f0f0f0 !important;
    border-radius: 8px !important;
    background: #fafafa !important;
    transition: all 0.2s ease !important;
}

.workspace-button:hover {
    border-color: #1890ff !important;
    background: #f6ffed !important;
}

.workspace-info {
    flex: 1;
    min-width: 0;
}

.workspace-name {
    font-weight: 600;
    font-size: 14px;
    color: #262626;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.workspace-description {
    font-size: 12px;
    color: #8c8c8c;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Legacy workspace list styles - keeping for backwards compatibility but hidden */
.workspace-list {
    display: none;
}

.workspace-item,
.workspace-avatar,
.workspace-details,
.workspace-boards-count {
    display: none;
}

/* Modal Actions */
.modal-actions {
    margin-bottom: 0 !important;
    text-align: right;
}

/* Sidebar Section Spacing */
.workspace-section {
    padding-bottom: 0 !important;
}

.sidebar-section {
    margin-bottom: 24px;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

/* Board List Container with Scroll */
.boards-container {
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 4px;
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

.boards-container::-webkit-scrollbar {
    width: 4px;
}

.boards-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.boards-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.boards-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Smooth scrolling for better UX */
.boards-container {
    scroll-behavior: smooth;
}

/* Empty Boards State */
.empty-boards {
    padding: 16px;
    text-align: center;
    color: #8c8c8c;
    background: #fafafa;
    border-radius: 8px;
    margin-bottom: 8px;
}

.empty-boards p {
    margin-bottom: 12px;
    font-size: 13px;
}

/* Board Item Enhancements */
.board-item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.list-info {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
    gap: 8px;
}

.list-name {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Section Headers */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.section-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #262626;
    display: flex;
    align-items: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .workspace-button {
        min-height: 44px;
        padding: 6px 10px !important;
    }

    .workspace-name {
        font-size: 13px;
    }

    .workspace-description {
        font-size: 11px;
    }

    .workspace-item {
        padding: 6px 10px;
    }

    .workspace-avatar {
        width: 28px;
        height: 28px;
        font-size: 12px;
        margin-right: 10px;
    }

    /* Mobile scroll adjustments */
    .boards-container {
        max-height: 250px;
    }

    /* Workspace dropdown scroll adjustments for mobile */
    .workspace-dropdown-overlay .ant-dropdown-menu {
        max-height: 250px;
    }
}

@media (max-width: 576px) {
    .boards-container {
        max-height: 200px;
    }

    .workspace-dropdown-overlay .ant-dropdown-menu {
        max-height: 200px;
    }
}

@media (max-width: 480px) {
    .boards-container {
        max-height: 180px;
    }

    .workspace-dropdown-overlay .ant-dropdown-menu {
        max-height: 180px;
    }
}

/* Empty States */
.empty-board-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: calc(100vh - 200px);
    padding: 40px;
}

/* Workspace Member Management Styles */
.workspace-member-modal .ant-modal-content {
    padding: 0;
}

.workspace-member-modal .ant-modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.member-search-container {
    margin-bottom: 20px;
}

.member-search-results {
    margin-top: 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    max-height: 200px;
    overflow-y: auto;
    background: #fff;
}

.member-search-results .ant-list-item {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s;
}

.member-search-results .ant-list-item:hover {
    background-color: #f8f9fa;
}

.member-search-results .ant-list-item:last-child {
    border-bottom: none;
}

.member-list-container {
    border-top: 1px solid #f0f0f0;
    padding-top: 16px;
}

.member-list-header {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #262626;
}

.member-item {
    padding: 12px 0;
    border-bottom: 1px solid #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.member-item:last-child {
    border-bottom: none;
}

.member-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.member-info .ant-avatar {
    border: 2px solid #f0f0f0;
}

.member-details {
    flex: 1;
}

.member-name {
    font-weight: 500;
    color: #262626;
    display: flex;
    align-items: center;
    gap: 8px;
}

.member-email {
    color: #8c8c8c;
    font-size: 12px;
    margin-top: 2px;
}

.member-tag {
    font-size: 11px;
    padding: 2px 8px;
    border-radius: 12px;
}

.member-actions {
    display: flex;
    gap: 8px;
}

.remove-member-btn {
    color: #ff4d4f;
    border-color: #ff4d4f;
}

.remove-member-btn:hover {
    background-color: #fff2f0;
    border-color: #ff7875;
    color: #ff7875;
}

/* Workspace Member Counter */
.workspace-member-counter {
    display: flex;
    align-items: center;
    gap: 4px;
}

.workspace-member-counter .ant-avatar-group {
    margin-right: 4px;
}

.workspace-member-counter .member-count-text {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
}

/* Workspace Dropdown Optimization */
.workspace-dropdown-overlay {
    min-width: 280px !important;
}

.workspace-dropdown-overlay .ant-dropdown-menu {
    padding: 8px 0;
    border-radius: 8px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    max-height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

.workspace-dropdown-overlay .ant-dropdown-menu::-webkit-scrollbar {
    width: 4px;
}

.workspace-dropdown-overlay .ant-dropdown-menu::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.workspace-dropdown-overlay .ant-dropdown-menu::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.workspace-dropdown-overlay .ant-dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Smooth scrolling for workspace dropdown */
.workspace-dropdown-overlay .ant-dropdown-menu {
    scroll-behavior: smooth;
}

.workspace-dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    min-height: 48px;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.workspace-dropdown-item:hover {
    background-color: #f5f5f5;
}

.workspace-avatar-small {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 12px;
    flex-shrink: 0;
}

.workspace-info-dropdown {
    flex: 1;
    min-width: 0;
}

.workspace-name-dropdown {
    font-weight: 500;
    color: #262626;
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.workspace-description-dropdown {
    font-size: 12px;
    color: #8c8c8c;
    line-height: 1.3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.workspace-selected-indicator {
    color: #1890ff;
    font-weight: bold;
    font-size: 16px;
    flex-shrink: 0;
}

.workspace-action-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    min-height: 40px;
    transition: all 0.2s;
    cursor: pointer;
}

.workspace-action-item .anticon {
    width: 16px;
    font-size: 14px;
    color: #8c8c8c;
}

.workspace-action-item span {
    color: #262626;
    font-size: 14px;
}

.workspace-action-item:hover {
    background-color: #f5f5f5;
}

.workspace-action-item:hover .anticon,
.workspace-action-item:hover span {
    color: #1890ff;
}

.workspace-action-item.create-action:hover {
    background-color: #f6ffed;
}

.workspace-action-item.create-action:hover .anticon,
.workspace-action-item.create-action:hover span {
    color: #52c41a;
}

.workspace-action-item.delete-action .anticon,
.workspace-action-item.delete-action span {
    color: #ff4d4f;
}

.workspace-action-item.delete-action:hover {
    background-color: #fff2f0;
}

.workspace-action-item.delete-action:hover .anticon,
.workspace-action-item.delete-action:hover span {
    color: #ff7875;
}

/* Update workspace button */
.workspace-button {
    width: 100% !important;
    height: auto !important;
    padding: 12px 16px !important;
    border: 1px solid #d9d9d9 !important;
    border-radius: 8px !important;
    background: #fff !important;
    text-align: left !important;
    transition: all 0.2s ease !important;
}

.workspace-button:hover {
    border-color: #1890ff !important;
    background: #f6ffed !important;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1) !important;
}

.workspace-button .ant-space {
    width: 100%;
    justify-content: space-between;
}

.workspace-button .workspace-info {
    flex: 1;
    min-width: 0;
    text-align: left;
}

.workspace-button .anticon-down {
    color: #8c8c8c;
    font-size: 12px;
}

/* Remove old workspace list styles */
.workspace-list {
    display: none;
}

/* Search styles */
.search-container {
    position: relative;
}

/* Board Assignee Styles */
.board-assignee-item {
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
}

.board-assignee-item:hover .remove-assignee-btn {
    display: flex !important;
}

.board-assignee-item:hover .ant-avatar {
    transform: scale(1.05);
}

.board-assignee-item .remove-assignee-btn {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 16px;
    height: 16px;
    background-color: #ff4d4f;
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 10px;
    color: white;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    z-index: 10;
    transition: all 0.2s ease;
}

.board-assignee-item .remove-assignee-btn:hover {
    background-color: #ff1f1f;
    transform: scale(1.1);
}

/* Thông báo */
.notification-item {
    position: relative;
    padding: 12px 40px 12px 16px;
    border-radius: 6px;
    margin-bottom: 4px;
    transition: background 0.2s;
    cursor: pointer;
    background: #f6f9ff;
    font-weight: 500;
}
.notification-item.isRead {
    background: #fff;
    font-weight: 400;
    color: #888;
}
.notification-item .unread-dot {
    width: 8px;
    height: 8px;
    background: #1890ff;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}
.notification-item:hover {
    background: #e6f7ff;
}
.delete-btn {
    display: none;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: #ff4d4f;
    font-size: 16px;
    cursor: pointer;
}
.notification-item:hover .delete-btn {
    display: block;
}
