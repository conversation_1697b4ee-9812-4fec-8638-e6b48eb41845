module.exports = (sequelize, DataTypes) => {
    const TaskComment = sequelize.define('TaskComment', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      taskId: {
        type: DataTypes.STRING(255),
        field: 'task_id',
        allowNull: false
      },
      userId: {
        type: DataTypes.INTEGER,
        field: 'user_id',
        allowNull: false
      },
      content: {
        type: DataTypes.TEXT,
        allowNull: false
      }
    }, {
      tableName: 'task_comments',
      timestamps: false
    });
  
    TaskComment.associate = (models) => {
      TaskComment.belongsTo(models.Task, {
        foreignKey: 'task_id',
        as: 'task',
        onDelete: 'CASCADE'
      });
      TaskComment.belongsTo(models.User, {
        foreignKey: 'user_id',
        as: 'user'
      });
    };
  
    return TaskComment;
  };