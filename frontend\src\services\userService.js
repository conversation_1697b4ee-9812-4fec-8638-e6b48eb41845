import axios from '../utils/axiosCustomize';

/**
 * L<PERSON>y thông tin người dùng hiện tại
 */
const getCurrentUser = async () => {
        const response = await axios.get('/users/me');
        return response.data;
};

/**
 * Tìm kiếm người dùng theo email hoặc tên
 */
const searchUsers = async (query) => {
        const response = await axios.get(`/users/search?q=${encodeURIComponent(query)}`);
        return response.data;
};

/**
 * L<PERSON>y thông tin tất cả người dùng
 */
const getAllUsers = async () => {
        const response = await axios.get('/users');
        return response.data;
};

/**
 * Lấy thông tin người dùng theo ID
 */
const getUserById = async (userId) => {
        const response = await axios.get(`/users/${userId}`);
        return response.data;
};

/**
 * <PERSON><PERSON><PERSON> thông tin người dùng theo email
 */
const getUserByEmail = async (email) => {
        const response = await axios.get(`/users/email/${encodeURIComponent(email)}`);
        return response.data;
};

export default {
    getCurrentUser,
    searchUsers,
    getAllUsers,
    getUserById,
    getUserByEmail
}; 