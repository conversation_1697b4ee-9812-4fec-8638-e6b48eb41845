import axios from '../utils/axiosCustomize';

const addAssigneeBoard = async (boardId, email) => {
    const response = await axios.post(`/board-assign/add-assignee`, { boardId, email });
    return response.data;
}

const removeAssigneeFromBoard = async (boardId, email) => {
    const response = await axios.post(`/board-assign/remove-assignee`, { boardId, email });
    return response.data;
}

const getBoardAssignees = async (boardId) => {
    const response = await axios.get(`/board-assign/${boardId}`);
    return response.data;
}

const getUserAssignedBoards = async () => {
    const response = await axios.get(`/board-assign/user-boards`);
    return response.data;
}

export { addAssigneeBoard, removeAssigneeFromBoard, getBoardAssignees, getUserAssignedBoards };