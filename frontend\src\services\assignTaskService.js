import axios from "../utils/axiosCustomize";

const addAssigneeToTask = async (taskId, email) => {
    const response = await axios.post(`/assign/add-assignee`, {
        taskId,
        email,
    });
    return response.data;
};

const removeAssigneeFromTask = async (taskId, email) => {
    const response = await axios.post(`/assign/remove-assignee`, {
        taskId,
        email,
    });
    return response.data;
};

const getTaskAssignees = async (taskId) => {
    const response = await axios.get(`/assign/${taskId}`);
    return response.data;
};

const updateTaskAssignees = async (taskId, emails) => {
    const response = await axios.post(`/assign/update-assignees`, {
        taskId,
        emails,
    });
    return response.data;
};

const clearTaskAssignees = async (taskId) => {
    const response = await axios.delete(`/assign/clear-assignees/${taskId}`);
    return response.data;
};

const getUserAssignedTasks = async () => {
    const response = await axios.get(`/assign/user-assigned-tasks`);
    return response.data;
};

export default {
    addAssigneeToTask,
    removeAssigneeFromTask,
    getTaskAssignees,
    updateTaskAssignees,
    clearTaskAssignees,
    getUserAssignedTasks,
};
