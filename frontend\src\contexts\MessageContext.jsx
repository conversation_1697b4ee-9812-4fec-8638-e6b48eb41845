import React, { createContext, useContext } from 'react';
import { message } from 'antd';

const MessageContext = createContext();

export const MessageProvider = ({ children }) => {
    const [messageApi, contextHolder] = message.useMessage({
        maxCount: 3
    });

    const showMessage = (type, content, duration = 5) => {
        messageApi.open({
            type,
            content,
            duration
        });
    };

    const success = (content, duration) => {
        showMessage('success', content, duration);
    };

    const error = (content, duration) => {
        showMessage('error', content, duration);
    };

    const warning = (content, duration) => {
        showMessage('warning', content, duration);
    };

    const info = (content, duration) => {
        showMessage('info', content, duration);
    };

    const loading = (content, duration) => {
        showMessage('loading', content, duration);
    };

    const value = {
        success,
        error,
        warning,
        info,
        loading,
        messageApi
    };

    return (
        <MessageContext.Provider value={value}>
            {contextHolder}
            {children}
        </MessageContext.Provider>
    );
};

export const useMessage = () => {
    const context = useContext(MessageContext);
    if (!context) {
        throw new Error('useMessage phải được sử dụng trong MessageProvider');
    }
    return context;
};

export default MessageContext;
