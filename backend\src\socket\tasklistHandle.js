function registerTaskListSocketHandlers(socket, emitToBoard) {
    // Lắng nghe sự kiện sau khi tasklist đư<PERSON><PERSON> tạo từ API
    socket.on("taskList:created", (data) => {
        const { boardId, taskList } = data;
        // console.log("TaskList created event received:", data);
        emitToBoard(boardId, "taskList:created", taskList);
    });

    // Lắng nghe sự kiện sau khi tasklist đ<PERSON><PERSON><PERSON> cập nhật từ API
    socket.on("taskList:updated", (data) => {
        const { boardId, taskList } = data;
        // console.log("TaskList updated event received:", data);
        emitToBoard(boardId, "taskList:updated", taskList);
    });

    // Xoá task list
    socket.on("taskList:deleted", async (data) => {
        const { boardId, taskListId } = data;
        // console.log("TaskList deleted event received:", data);
        emitToBoard(boardId, "taskList:deleted", { id: taskListId });
    });

    // Di chuyển/thay đổi thứ tự task list
    socket.on("taskList:moved", async (data) => {
        const { boardId, taskList } = data;
        // console.log("TaskList moved event received:", data);
        emitToBoard(boardId, "taskList:moved", taskList);
    });
}

module.exports = { registerTaskListSocketHandlers };
