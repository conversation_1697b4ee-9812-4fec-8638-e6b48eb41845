import React, { useState, useEffect, useCallback } from "react";
import {
  Popover,
  Button,
  Input,
  List,
  Space,
  Checkbox,
  message,
  Divider,
  Empty,
  Spin,
} from "antd";
import {
  TagOutlined,
  PlusOutlined,
  SearchOutlined,
  SettingOutlined,
} from "@ant-design/icons";
import labelService from "../../services/labelService";
import LabelBadge from "./LabelBadge";
import LabelManager from "./LabelManager";
import "./LabelComponents.css";

const LabelSelector = ({
  taskId,
  boardId,
  taskLabels = [],
  onLabelsChange,
  onRefreshLabels,
  onLabelManagerStateChange,
  onPopoverStateChange,
  onRefreshAllLabels, // Thêm prop để refresh tất cả labels
  trigger = "click",
  placement = "bottomLeft",
  children,
}) => {
  const [visible, setVisible] = useState(false);
  const [allLabels, setAllLabels] = useState([]);
  const [filteredLabels, setFilteredLabels] = useState([]);
  const [searchText, setSearchText] = useState("");
  const [loading, setLoading] = useState(false);
  const [showLabelManager, setShowLabelManager] = useState(false);

  const fetchBoardLabels = useCallback(async () => {
    try {
      setLoading(true);
      const labels = await labelService.getLabelsByBoardId(boardId);
      setAllLabels(labels || []);
    } catch (error) {
      message.error("Không thể tải danh sách labels");
      console.error("Error fetching labels:", error);
    } finally {
      setLoading(false);
    }
  }, [boardId]);

  useEffect(() => {
    if (visible && boardId) {
      fetchBoardLabels();
    }
  }, [visible, boardId, fetchBoardLabels]);

  useEffect(() => {
    // Filter labels based on search text
    if (searchText) {
      const filtered = allLabels.filter((label) =>
        label.name.toLowerCase().includes(searchText.toLowerCase())
      );
      setFilteredLabels(filtered);
    } else {
      setFilteredLabels(allLabels);
    }
  }, [searchText, allLabels]);

  const handleLabelToggle = async (label, checked) => {
    try {
      if (checked) {
        await labelService.addLabelToTask(taskId, label.id);
        message.success(`Đã thêm label "${label.name}"`);
      } else {
        await labelService.removeLabelFromTask(taskId, label.id);
        message.success(`Đã xóa label "${label.name}"`);
      }

      // Cập nhật danh sách labels của task
      if (onLabelsChange) {
        const updatedLabels = checked
          ? [...taskLabels, label]
          : taskLabels.filter((l) => l.id !== label.id);
        onLabelsChange(updatedLabels);
      }
    } catch (error) {
      message.error("Không thể cập nhật label");
      console.error("Error updating label:", error);
    }
  };

  const isLabelSelected = (labelId) => {
    return taskLabels.some((label) => label.id === labelId);
  };

  const handleVisibleChange = (newVisible) => {
    setVisible(newVisible);
    if (onPopoverStateChange) {
      onPopoverStateChange(newVisible);
    }
    if (!newVisible) {
      setSearchText("");
    }
  };

  const renderLabelItem = (label) => {
    const isSelected = isLabelSelected(label.id);

    const handleItemClick = (e) => {
      // Chỉ xử lý click nếu không phải click vào checkbox hoặc các element con của checkbox
      const isCheckboxClick =
        e.target.closest(".ant-checkbox-wrapper") ||
        e.target.closest(".ant-checkbox") ||
        e.target.type === "checkbox";

      if (!isCheckboxClick) {
        handleLabelToggle(label, !isSelected);
      }
    };

    return (
      <List.Item
        style={{
          padding: "8px 0",
          cursor: "pointer",
          borderRadius: "4px",
          margin: "2px 0",
        }}
        onClick={handleItemClick}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            width: "100%",
          }}
        >
          <Checkbox
            checked={isSelected}
            onChange={(e) => handleLabelToggle(label, e.target.checked)}
            style={{ marginRight: 8 }}
            onClick={(e) => e.stopPropagation()}
          />
          <LabelBadge label={label} style={{ margin: 0, flex: 1 }} />
        </div>
      </List.Item>
    );
  };

  const content = (
    <div style={{ width: 280, maxHeight: 400 }}>
      {/* Header với search */}
      <div style={{ marginBottom: 12 }}>
        <Input
          placeholder="Tìm kiếm labels..."
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          allowClear
        />
      </div>

      {/* Danh sách labels */}
      <div style={{ maxHeight: 200, overflowY: "auto" }}>
        {loading ? (
          <div style={{ textAlign: "center", padding: 20 }}>
            <Spin />
          </div>
        ) : filteredLabels.length > 0 ? (
          <List
            dataSource={filteredLabels}
            renderItem={renderLabelItem}
            size="small"
          />
        ) : (
          <Empty
            description={
              searchText ? "Không tìm thấy label" : "Chưa có label nào"
            }
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </div>

      <Divider style={{ margin: "12px 0" }} />

      {/* Footer actions */}
      <Space style={{ width: "100%", justifyContent: "space-between" }}>
        {/* <Button
                    type='text'
                    icon={<PlusOutlined />}
                    size='small'
                    onClick={() => {
                        setShowLabelManager(true);
                        setVisible(false);
                    }}
                >
                    Tạo label
                </Button> */}
        <Button
          type="text"
          icon={<SettingOutlined />}
          size="small"
          onClick={() => {
            setShowLabelManager(true);
            setVisible(false);
            if (onLabelManagerStateChange) {
              onLabelManagerStateChange(true);
            }
          }}
        >
          Quản lý
        </Button>
      </Space>
    </div>
  );

  return (
    <>
      <Popover
        content={content}
        title="Chọn Labels"
        trigger={trigger}
        placement={placement}
        open={visible}
        onOpenChange={handleVisibleChange}
        overlayClassName="label-selector-popover"
      >
        {children || (
          <Button
            type="text"
            icon={<TagOutlined />}
            size="small"
            style={{ color: "#8c8c8c" }}
          >
            Labels
          </Button>
        )}
      </Popover>

      <LabelManager
        visible={showLabelManager}
        onClose={() => {
          setShowLabelManager(false);
          if (onLabelManagerStateChange) {
            onLabelManagerStateChange(false);
          }
          if (visible) {
            fetchBoardLabels(); // Refresh labels when manager closes
          }
        }}
        boardId={boardId}
        onLabelsUpdated={() => {
          // Refresh task labels when labels are updated in manager
          if (onRefreshLabels) {
            onRefreshLabels();
          }
          // Refresh tất cả labels trong board
          if (onRefreshAllLabels) {
            onRefreshAllLabels();
          }
        }}
      />
    </>
  );
};

export default LabelSelector;
