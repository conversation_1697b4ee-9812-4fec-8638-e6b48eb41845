const { uploadFileToDrive, deleteFileFromDrive } = require('../utils/upload');
const { TaskAttachment, User, Task } = require('../models');

class AttachmentService {
    static async uploadFileToTask(taskId, file, userId) {
        try {
            
            //Check task có tồn tại không
            const task = await Task.findByPk(taskId);
            if(!task) {
                throw new Error('Task không tồn tại');
            }

            //Check user có tồn tại không
            const user = await User.findByPk(userId);
            if(!user) {
                throw new Error('User không tồn tại');
            }

            //Check file có tồn tại không
            if(!file) {
                throw new Error('File không tồn tại');
            }

            //upload file lên google drive
            const uploadedFile = await uploadFileToDrive(file, userId);

            // console.log("uploadedFile:", uploadedFile);
            // console.log("uploadedFile ID:", uploadedFile.id);
            

            //Tạo attachment mới trong database
            const attachment = await TaskAttachment.create({
                id: uploadedFile.id,
                taskId: taskId,
                uploadedBy: userId,
                fileName: uploadedFile.name,
                mimeType: uploadedFile.mimeType,
                fileSize: uploadedFile.size,
                webViewLink: uploadedFile.webViewLink,
                webContentLink: uploadedFile.webContentLink,
                createdAt: new Date(),
            })

            console.log("attachment:", attachment);

            return attachment;
        } catch (error) {
            console.error('Error uploading file to task:', error);
            throw error;
        }
    }

    static async deleteFileFromTask(taskId, fileId, userId) {
        try {
            //Check task có tồn tại không
            const task = await Task.findByPk(taskId);
            if(!task) {
                throw new Error('Task không tồn tại');
            }

            //Chỉ người upload mới xóa được
            const attachment = await TaskAttachment.findByPk(fileId);
            if(attachment.uploadedBy !== userId) {
                throw new Error('Bạn không có quyền xóa file này');
            }

            //Xóa file trong google drive
            await deleteFileFromDrive(fileId, userId);

            //Xóa attachment trong database
            await TaskAttachment.destroy({
                where: { id: fileId, taskId: taskId }
            })

            return {
                success: true,
                message: 'Xóa file thành công'
            }
            
        } catch (error) {
            console.error('Error deleting file from task:', error);
            throw error;
        }
    }

    static async getTaskAttachments(taskId) {
        try {
            //Check task có tồn tại không
            const task = await Task.findByPk(taskId);
            if(!task) {
                throw new Error('Task không tồn tại');
            }

            //Lấy danh sách attachments của task
            const attachments = await TaskAttachment.findAll({
                where: { taskId: taskId },
                order: [['created_at', 'DESC']]
            })

            return attachments;
        } catch (error) {
            console.error('Error getting task attachments:', error);
            throw error;
        }
    }
}

module.exports = AttachmentService;