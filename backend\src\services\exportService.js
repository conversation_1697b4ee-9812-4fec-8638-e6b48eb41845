const { Board, TaskList, Task, User, TaskAssignee } = require('../models');
const { getAuthenticatedSheetClient } = require('../middleware/googleAuth');
const { Op } = require('sequelize');

//Xuất dữ liệu từ board ra Google Sheet với cấu trúc cải tiến
const exportBoardToSheet = async (boardId, userId) => {
    try {
      // Lấy thông tin board
      const board = await Board.findByPk(boardId, {
        include: [
          {
            model: User,
            as: "createdByUser",
            attributes: ["id", "fullName", "email"],
          },
        ],
      });
  
      if (!board) {
        throw new Error("Board không tồn tại");
      }
  
      // Lấy danh sách task list trong board
      const taskLists = await TaskList.findAll({
        where: { boardId },
        include: [
          {
            model: Task,
            as: "tasks",
            include: [
              {
                model: User,
                as: "creator",
                attributes: ["id", "fullName", "email"],
              },
              {
                model: User,
                as: "assignees",
                attributes: ["id", "fullName", "email"],
                through: { attributes: [] },
              },
            ],
          },
        ]
      });
  
      // Lấy Google Sheets API client
      const sheets = await getAuthenticatedSheetClient(userId);
  
      // Tạo spreadsheet mới với multiple sheets
      const spreadsheet = await sheets.spreadsheets.create({
        requestBody: {
          properties: {
            title: `${board.name} - Export ${new Date().toLocaleDateString("vi-VN")}`,
          },
          sheets: [
            {
              properties: {
                title: "Tổng Quan",
                gridProperties: {
                  rowCount: 100,
                  columnCount: 10
                }
              },
            },
            {
              properties: {
                title: "Chi Tiết Tasks",
                gridProperties: {
                  rowCount: 1000,
                  columnCount: 15
                }
              },
            },
            {
              properties: {
                title: "Thống Kê",
                gridProperties: {
                  rowCount: 50,
                  columnCount: 8
                }
              },
            }
          ],
        },
      });
  
      const spreadsheetId = spreadsheet.data.spreadsheetId;
      const sheetIds = {
        overview: spreadsheet.data.sheets[0].properties.sheetId,
        details: spreadsheet.data.sheets[1].properties.sheetId,
        statistics: spreadsheet.data.sheets[2].properties.sheetId
      };
      
      // Tạo sheet tổng quan
      await createOverviewSheet(sheets, spreadsheetId, board, taskLists, sheetIds.overview);
      
      // Tạo sheet chi tiết tasks
      await createDetailedTasksSheet(sheets, spreadsheetId, taskLists, sheetIds.details);
      
      // Tạo sheet thống kê
      await createStatisticsSheet(sheets, spreadsheetId, taskLists, sheetIds.statistics);
  
      return {
        spreadsheetId,
        spreadsheetUrl: `https://docs.google.com/spreadsheets/d/${spreadsheetId}`,
        title: spreadsheet.data.properties.title,
      };
    } catch (error) {
      console.error("Lỗi khi xuất dữ liệu ra Google Sheet:", error.message);
      throw error;
    }
};

// Tạo sheet tổng quan với căn chỉnh cải thiện
const createOverviewSheet = async (sheets, spreadsheetId, board, taskLists, sheetId) => {
  
  // Thông tin board
  const boardInfo = [
    ["THÔNG TIN BOARD", ""],
    ["Tên Board:", board.name],
    ["Người tạo:", board.createdByUser?.fullName || "N/A"],
    ["Ngày tạo:", new Date(board.createdAt).toLocaleDateString("vi-VN")],
    ["Ngày xuất:", new Date().toLocaleDateString("vi-VN")],
    [""],
    ["TỔNG QUAN NHANH", ""],
    ["Tổng số Task Lists:", taskLists.length],
    ["Tổng số Tasks:", taskLists.reduce((sum, tl) => sum + tl.tasks.length, 0)],
    ["Tasks hoàn thành:", taskLists.reduce((sum, tl) => sum + tl.tasks.filter(t => t.status === 'completed').length, 0)],
    ["Tasks đang thực hiện:", taskLists.reduce((sum, tl) => sum + tl.tasks.filter(t => t.status !== 'completed').length, 0)],
    [""],
  ];

  // Thêm thống kê cho từng task list
  boardInfo.push(["CHI TIẾT TASK LISTS", "Số Tasks", "Hoàn thành", "Đang thực hiện"]);
  
  taskLists.forEach(taskList => {
    const totalTasks = taskList.tasks.length;
    const completedTasks = taskList.tasks.filter(t => t.status === 'completed').length;
    const pendingTasks = totalTasks - completedTasks;
    
    boardInfo.push([
      `${taskList.title}`,
      totalTasks,
      completedTasks,
      pendingTasks
    ]);
  });

  // Ghi dữ liệu vào sheet
  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: "Tổng Quan!A1",
    valueInputOption: "USER_ENTERED",
    requestBody: {
      values: boardInfo,
    },
  });

  // Định dạng sheet tổng quan với căn chỉnh cải thiện
  await sheets.spreadsheets.batchUpdate({
    spreadsheetId,
    requestBody: {
      requests: [
        // Thiết lập độ rộng cột cố định
        {
          updateDimensionProperties: {
            range: {
              sheetId: sheetId,
              dimension: "COLUMNS",
              startIndex: 0,
              endIndex: 1,
            },
            properties: {
              pixelSize: 250,
            },
            fields: "pixelSize",
          },
        },
        {
          updateDimensionProperties: {
            range: {
              sheetId: sheetId,
              dimension: "COLUMNS", 
              startIndex: 1,
              endIndex: 4,
            },
            properties: {
              pixelSize: 120,
            },
            fields: "pixelSize",
          },
        },
        // Header chính (màu xanh đậm) với căn giữa
        {
          repeatCell: {
            range: {
              sheetId: sheetId,
              startRowIndex: 0,
              endRowIndex: 1,
            },
            cell: {
              userEnteredFormat: {
                backgroundColor: { red: 0.0, green: 0.4, blue: 0.8 },
                textFormat: { 
                  foregroundColor: { red: 1.0, green: 1.0, blue: 1.0 },
                  bold: true,
                  fontSize: 14
                },
                horizontalAlignment: "CENTER",
                verticalAlignment: "MIDDLE",
              },
            },
            fields: "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment,verticalAlignment)",
          },
        },
        // Sub headers (màu xanh nhạt) với căn giữa
        {
          repeatCell: {
            range: {
              sheetId: sheetId,
              startRowIndex: 6,
              endRowIndex: 7,
            },
            cell: {
              userEnteredFormat: {
                backgroundColor: { red: 0.8, green: 0.9, blue: 1.0 },
                textFormat: { bold: true },
                horizontalAlignment: "CENTER",
                verticalAlignment: "MIDDLE",
              },
            },
            fields: "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment,verticalAlignment)",
          },
        },
        // Header bảng task lists với căn giữa
        {
          repeatCell: {
            range: {
              sheetId: sheetId,
              startRowIndex: 12,
              endRowIndex: 13,
            },
            cell: {
              userEnteredFormat: {
                backgroundColor: { red: 0.2, green: 0.6, blue: 0.2 },
                textFormat: { 
                  foregroundColor: { red: 1.0, green: 1.0, blue: 1.0 },
                  bold: true 
                },
                horizontalAlignment: "CENTER",
                verticalAlignment: "MIDDLE",
              },
            },
            fields: "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment,verticalAlignment)",
          },
        },
        // Căn trái cho cột đầu tiên (labels)
        {
          repeatCell: {
            range: {
              sheetId: sheetId,
              startColumnIndex: 0,
              endColumnIndex: 1,
            },
            cell: {
              userEnteredFormat: {
                horizontalAlignment: "LEFT",
                verticalAlignment: "MIDDLE",
              },
            },
            fields: "userEnteredFormat(horizontalAlignment,verticalAlignment)",
          },
        },
        // Căn giữa cho các cột số liệu
        {
          repeatCell: {
            range: {
              sheetId: sheetId,
              startColumnIndex: 1,
              endColumnIndex: 4,
            },
            cell: {
              userEnteredFormat: {
                horizontalAlignment: "CENTER",
                verticalAlignment: "MIDDLE",
              },
            },
            fields: "userEnteredFormat(horizontalAlignment,verticalAlignment)",
          },
        },
        // Thêm border cho toàn bộ dữ liệu
        {
          updateBorders: {
            range: {
              sheetId: sheetId,
              startRowIndex: 0,
              endRowIndex: boardInfo.length,
              startColumnIndex: 0,
              endColumnIndex: 4,
            },
            top: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
            bottom: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
            left: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
            right: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
            innerHorizontal: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
            innerVertical: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
          },
        },
      ],
    },
  });
};

// Tạo sheet chi tiết tasks với căn chỉnh cải thiện
const createDetailedTasksSheet = async (sheets, spreadsheetId, taskLists, sheetId) => {
  
  const headerRow = [
    "Task List",
    "Tên Task", 
    "Mô tả",
    "Trạng thái",
    "Hạn hoàn thành",
    "Người tạo",
    "Người thực hiện",
    "Ngày tạo",
    "Độ ưu tiên",
    "Tiến độ (%)"
  ];

  const rows = [headerRow];
  
  // Nhóm tasks theo task list và thêm separator
  taskLists.forEach((taskList, index) => {
    // Thêm separator cho task list
    if (index > 0) {
      rows.push(["", "", "", "", "", "", "", "", "", ""]);
    }
    
    // Thêm header cho task list
    rows.push([
      `${taskList.title.toUpperCase()}`,
      `${taskList.tasks.length} tasks`,
      "", "", "", "", "", "", "", ""
    ]);
    
    // Thêm tasks
    taskList.tasks.forEach(task => {
      const assigneeNames = task.assignees.map(u => u.fullName).join(", ");
      const status = task.status === "completed" ? "Hoàn thành" : "Đang thực hiện";
      const progress = task.status === "completed" ? 100 : 
                      task.status === "in-progress" ? 50 : 0;
      
      rows.push([
        taskList.title,
        task.title,
        task.notes || "",
        status,
        task.due ? new Date(task.due).toLocaleDateString("vi-VN") : "",
        task.creator?.fullName || "",
        assigneeNames,
        new Date(task.created_at).toLocaleDateString("vi-VN"),
        task.priority || "Bình thường"
      ]);
    });
  });

  // Ghi dữ liệu
  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: "Chi Tiết Tasks!A1",
    valueInputOption: "USER_ENTERED",
    requestBody: {
      values: rows,
    },
  });

  // Định dạng sheet chi tiết với căn chỉnh cải thiện
  await sheets.spreadsheets.batchUpdate({
    spreadsheetId,
    requestBody: {
      requests: [
        // Thiết lập độ rộng cột
        {
          updateDimensionProperties: {
            range: {
              sheetId: sheetId,
              dimension: "COLUMNS",
              startIndex: 0,
              endIndex: 1, // Task List
            },
            properties: {
              pixelSize: 150,
            },
            fields: "pixelSize",
          },
        },
        {
          updateDimensionProperties: {
            range: {
              sheetId: sheetId,
              dimension: "COLUMNS",
              startIndex: 1,
              endIndex: 2, // Tên Task
            },
            properties: {
              pixelSize: 200,
            },
            fields: "pixelSize",
          },
        },
        {
          updateDimensionProperties: {
            range: {
              sheetId: sheetId,
              dimension: "COLUMNS",
              startIndex: 2,
              endIndex: 3, // Mô tả
            },
            properties: {
              pixelSize: 250,
            },
            fields: "pixelSize",
          },
        },
        {
          updateDimensionProperties: {
            range: {
              sheetId: sheetId,
              dimension: "COLUMNS",
              startIndex: 3,
              endIndex: 10, // Các cột còn lại
            },
            properties: {
              pixelSize: 120,
            },
            fields: "pixelSize",
          },
        },
        // Header chính với căn giữa
        {
          repeatCell: {
            range: {
              sheetId: sheetId,
              startRowIndex: 0,
              endRowIndex: 1,
            },
            cell: {
              userEnteredFormat: {
                backgroundColor: { red: 0.0, green: 0.5, blue: 0.0 },
                textFormat: { 
                  foregroundColor: { red: 1.0, green: 1.0, blue: 1.0 },
                  bold: true,
                  fontSize: 12
                },
                horizontalAlignment: "CENTER",
                verticalAlignment: "MIDDLE",
              },
            },
            fields: "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment,verticalAlignment)",
          },
        },
        // Căn trái cho cột mô tả (dễ đọc)
        {
          repeatCell: {
            range: {
              sheetId: sheetId,
              startColumnIndex: 2,
              endColumnIndex: 3,
            },
            cell: {
              userEnteredFormat: {
                horizontalAlignment: "LEFT",
                verticalAlignment: "TOP",
                wrapStrategy: "WRAP",
              },
            },
            fields: "userEnteredFormat(horizontalAlignment,verticalAlignment,wrapStrategy)",
          },
        },
        // Căn giữa cho các cột trạng thái, ngày tháng
        {
          repeatCell: {
            range: {
              sheetId: sheetId,
              startColumnIndex: 3,
              endColumnIndex: 10,
            },
            cell: {
              userEnteredFormat: {
                horizontalAlignment: "CENTER",
                verticalAlignment: "MIDDLE",
              },
            },
            fields: "userEnteredFormat(horizontalAlignment,verticalAlignment)",
          },
        },
        // Freeze header row
        {
          updateSheetProperties: {
            properties: {
              sheetId: sheetId,
              gridProperties: {
                frozenRowCount: 1,
              },
            },
            fields: "gridProperties.frozenRowCount",
          },
        },
        // Thêm border cho toàn bộ dữ liệu
        {
          updateBorders: {
            range: {
              sheetId: sheetId,
              startRowIndex: 0,
              endRowIndex: rows.length,
              startColumnIndex: 0,
              endColumnIndex: 10,
            },
            top: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
            bottom: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
            left: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
            right: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
            innerHorizontal: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
            innerVertical: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
          },
        },
      ],
    },
  });
};

// Tạo sheet thống kê với căn chỉnh cải thiện
const createStatisticsSheet = async (sheets, spreadsheetId, taskLists, sheetId) => {
  
  const totalTasks = taskLists.reduce((sum, tl) => sum + tl.tasks.length, 0);
  const completedTasks = taskLists.reduce((sum, tl) => 
    sum + tl.tasks.filter(t => t.status === 'completed').length, 0);
  const pendingTasks = totalTasks - completedTasks;
  
  const statsData = [
    ["THỐNG KÊ CHI TIẾT", ""],
    [""],
    ["TỔNG QUAN", "Số lượng", "Tỷ lệ %"],
    ["Tổng số Tasks", totalTasks, "100%"],
    ["Tasks hoàn thành", completedTasks, `${((completedTasks/totalTasks)*100).toFixed(1)}%`],
    ["Tasks đang thực hiện", pendingTasks, `${((pendingTasks/totalTasks)*100).toFixed(1)}%`],
    [""],
    ["PHÂN TÍCH THEO TASK LIST", "Tasks", "Hoàn thành", "Tỷ lệ hoàn thành"],
  ];

  // Thống kê theo task list
  taskLists.forEach(taskList => {
    const total = taskList.tasks.length;
    const completed = taskList.tasks.filter(t => t.status === 'completed').length;
    const percentage = total > 0 ? ((completed/total)*100).toFixed(1) : 0;
    
    statsData.push([
      taskList.title,
      total,
      completed,
      `${percentage}%`
    ]);
  });

  // Thêm thống kê theo người thực hiện
  const assigneeStats = new Map();
  taskLists.forEach(taskList => {
    taskList.tasks.forEach(task => {
      task.assignees.forEach(assignee => {
        if (!assigneeStats.has(assignee.id)) {
          assigneeStats.set(assignee.id, {
            name: assignee.fullName,
            total: 0,
            completed: 0
          });
        }
        const stats = assigneeStats.get(assignee.id);
        stats.total++;
        if (task.status === 'completed') {
          stats.completed++;
        }
      });
    });
  });

  if (assigneeStats.size > 0) {
    statsData.push([""], ["THỐNG KÊ THEO NGƯỜI THỰC HIỆN", "Tasks", "Hoàn thành", "Tỷ lệ"]);
    assigneeStats.forEach(stats => {
      const percentage = stats.total > 0 ? ((stats.completed/stats.total)*100).toFixed(1) : 0;
      statsData.push([
        stats.name,
        stats.total,
        stats.completed,
        `${percentage}%`
      ]);
    });
  }

  // Ghi dữ liệu
  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: "Thống Kê!A1",
    valueInputOption: "USER_ENTERED",
    requestBody: {
      values: statsData,
    },
  });

  // Định dạng sheet thống kê với căn chỉnh cải thiện
  await sheets.spreadsheets.batchUpdate({
    spreadsheetId,
    requestBody: {
      requests: [
        // Thiết lập độ rộng cột
        {
          updateDimensionProperties: {
            range: {
              sheetId: sheetId,
              dimension: "COLUMNS",
              startIndex: 0,
              endIndex: 1,
            },
            properties: {
              pixelSize: 250,
            },
            fields: "pixelSize",
          },
        },
        {
          updateDimensionProperties: {
            range: {
              sheetId: sheetId,
              dimension: "COLUMNS",
              startIndex: 1,
              endIndex: 4,
            },
            properties: {
              pixelSize: 120,
            },
            fields: "pixelSize",
          },
        },
        // Header chính với căn giữa
        {
          repeatCell: {
            range: {
              sheetId: sheetId,
              startRowIndex: 0,
              endRowIndex: 1,
            },
            cell: {
              userEnteredFormat: {
                backgroundColor: { red: 0.8, green: 0.4, blue: 0.0 },
                textFormat: { 
                  foregroundColor: { red: 1.0, green: 1.0, blue: 1.0 },
                  bold: true,
                  fontSize: 14
                },
                horizontalAlignment: "CENTER",
                verticalAlignment: "MIDDLE",
              },
            },
            fields: "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment,verticalAlignment)",
          },
        },
        // Sub headers với căn giữa
        {
          repeatCell: {
            range: {
              sheetId: sheetId,
              startRowIndex: 2,
              endRowIndex: 3,
            },
            cell: {
              userEnteredFormat: {
                backgroundColor: { red: 1.0, green: 0.9, blue: 0.8 },
                textFormat: { bold: true },
                horizontalAlignment: "CENTER",
                verticalAlignment: "MIDDLE",
              },
            },
            fields: "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment,verticalAlignment)",
          },
        },
        // Header bảng phân tích
        {
          repeatCell: {
            range: {
              sheetId: sheetId,
              startRowIndex: 7,
              endRowIndex: 8,
            },
            cell: {
              userEnteredFormat: {
                backgroundColor: { red: 1.0, green: 0.9, blue: 0.8 },
                textFormat: { bold: true },
                horizontalAlignment: "CENTER",
                verticalAlignment: "MIDDLE",
              },
            },
            fields: "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment,verticalAlignment)",
          },
        },
        // Căn trái cho cột đầu tiên (labels)
        {
          repeatCell: {
            range: {
              sheetId: sheetId,
              startColumnIndex: 0,
              endColumnIndex: 1,
            },
            cell: {
              userEnteredFormat: {
                horizontalAlignment: "LEFT",
                verticalAlignment: "MIDDLE",
              },
            },
            fields: "userEnteredFormat(horizontalAlignment,verticalAlignment)",
          },
        },
        // Căn giữa cho các cột số liệu
        {
          repeatCell: {
            range: {
              sheetId: sheetId,
              startColumnIndex: 1,
              endColumnIndex: 4,
            },
            cell: {
              userEnteredFormat: {
                horizontalAlignment: "CENTER",
                verticalAlignment: "MIDDLE",
              },
            },
            fields: "userEnteredFormat(horizontalAlignment,verticalAlignment)",
          },
        },
        // Thêm border cho toàn bộ dữ liệu
        {
          updateBorders: {
            range: {
              sheetId: sheetId,
              startRowIndex: 0,
              endRowIndex: statsData.length,
              startColumnIndex: 0,
              endColumnIndex: 4,
            },
            top: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
            bottom: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
            left: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
            right: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
            innerHorizontal: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
            innerVertical: { style: "SOLID", width: 1, color: { red: 0.8, green: 0.8, blue: 0.8 } },
          },
        },
      ],
    },
  });
};

module.exports = {
    exportBoardToSheet
};