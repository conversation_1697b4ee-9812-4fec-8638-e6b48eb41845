import React, { useState, useEffect, useCallback } from 'react';
import {
    Layout,
    Button,
    Input,
    Badge,
    Avatar,
    Dropdown,
    Spin,
    Empty,
    Typography
} from 'antd';
import {
    MenuFoldOutlined,
    MenuUnfoldOutlined,
    SearchOutlined,
    BellOutlined,
    UserOutlined,
    LogoutOutlined,
    SwapOutlined,
    DeleteOutlined,
    CloseCircleOutlined
} from '@ant-design/icons';
import axios from '../utils/axiosCustomize';
import { removeToken } from '../utils/tokenManager';
import {
    getNotifications,
    deleteNotification,
    markAsRead,
    markAllAsRead,
    deleteAllNotifications
} from '../services/notificationService';
import TaskService from '../services/taskService';
import { debounce } from 'lodash';

const { Header } = Layout;
const { Text } = Typography;

const MAX_NOTIFICATIONS_DISPLAY = 10; // <PERSON><PERSON><PERSON><PERSON> hạn số lượng thông báo hiển thị

const DashboardHeader = ({
    collapsed,
    onToggleCollapse,
    onSearchResult,
    selectedBoard,
    searchText,
    setSearchText
}) => {
    const [userMenuVisible, setUserMenuVisible] = useState(false);
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const [notifications, setNotifications] = useState([]);
    const [notificationVisible, setNotificationVisible] = useState(false);
    const [loadingNotifications, setLoadingNotifications] = useState(false);
    const [markingAllAsRead, setMarkingAllAsRead] = useState(false);
    const [deletingAllNotifications, setDeletingAllNotifications] =
        useState(false);

    const [searchLoading, setSearchLoading] = useState(false);

    const [userInfo, setUserInfo] = useState({
        name: 'Đang tải...',
        email: 'Đang tải...',
        photoUrl:
            'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face&auto=format'
    });

    useEffect(() => {
        fetchUserProfile();
        fetchNotifications();
    }, []);

    const fetchUserProfile = async () => {
        try {
            const response = await axios.get('/auth/profile');
            if (response.data.status === 'success') {
                setUserInfo({
                    name: response.data.data.fullName || 'Người dùng',
                    email: response.data.data.email || '',
                    photoUrl:
                        response.data.data.photoUrl ||
                        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face&auto=format'
                });
            }
        } catch (error) {
            console.error('Lỗi khi lấy thông tin người dùng:', error);
        }
    };

    const fetchNotifications = async () => {
        setLoadingNotifications(true);
        try {
            const response = await getNotifications();
            if (response.success) {
                setNotifications(response.data);
            }

            // console.log(response);
        } catch (error) {
            console.error('Lỗi khi lấy thông báo:', error);
            setNotifications([]);
        } finally {
            setLoadingNotifications(false);
        }
    };

    const handleDeleteNotification = async (id) => {
        try {
            await deleteNotification(id);
            setNotifications(notifications.filter((notif) => notif.id !== id));
        } catch (error) {
            console.error('Lỗi khi xóa thông báo:', error);
        }
    };

    const handleMarkAsRead = async (id) => {
        try {
            await markAsRead(id);
            setNotifications((prevNotifications) =>
                prevNotifications.map((notif) =>
                    notif.id === id ? { ...notif, isRead: true } : notif
                )
            );
        } catch (error) {
            console.error('Lỗi khi đánh dấu thông báo là đã đọc:', error);
        }
    };

    const handleLogout = async () => {
        setIsLoggingOut(true);
        setUserMenuVisible(false);
        try {
            await axios.get('/auth/logout');
        } catch (error) {
            console.error('Lỗi khi đăng xuất:', error);
        } finally {
            removeToken();
            window.location.href = '/';
        }
    };

    const handleSwitchAccount = () => {
        removeToken();
        window.location.href = '/';
    };

    const userMenuItems = [
        {
            key: 'profile',
            icon: <UserOutlined />,
            disabled: true,
            label: (
                <div className='user-info'>
                    <div className='user-name'>{userInfo.name}</div>
                    <div className='user-email'>{userInfo.email}</div>
                </div>
            )
        },
        {
            type: 'divider'
        },
        {
            key: 'switch',
            icon: <SwapOutlined />,
            label: 'Đổi tài khoản',
            onClick: handleSwitchAccount
        },
        {
            key: 'logout',
            icon: <LogoutOutlined />,
            label: isLoggingOut ? 'Đang đăng xuất...' : 'Đăng xuất',
            onClick: handleLogout,
            className: 'logout-item',
            disabled: isLoggingOut
        }
    ];

    const handleMarkAllAsRead = async () => {
        setMarkingAllAsRead(true);
        try {
            await markAllAsRead();
            // Cập nhật state để trigger re-render
            setNotifications((prevNotifications) =>
                prevNotifications.map((notif) => ({
                    ...notif,
                    isRead: true
                }))
            );
            // Đóng dropdown notification nếu đang mở
            setNotificationVisible(false);
        } catch (error) {
            console.error('Lỗi khi đánh dấu thông báo là đã đọc:', error);
        } finally {
            setMarkingAllAsRead(false);
        }
    };

    const handleDeleteAllNotifications = async () => {
        setDeletingAllNotifications(true);
        try {
            await deleteAllNotifications();
            // Xóa tất cả thông báo khỏi state
            setNotifications([]);
            // Đóng dropdown notification nếu đang mở
            setNotificationVisible(false);
        } catch (error) {
            console.error('Lỗi khi xóa tất cả thông báo:', error);
        } finally {
            setDeletingAllNotifications(false);
        }
    };

    const getNotificationItems = () => {
        if (loadingNotifications) {
            return [
                {
                    key: 'loading',
                    label: (
                        <div style={{ textAlign: 'center', padding: '10px' }}>
                            <Spin size='small' />
                            <Text style={{ marginLeft: '10px' }}>
                                Đang tải thông báo...
                            </Text>
                        </div>
                    )
                }
            ];
        }

        if (!notifications.length) {
            return [
                {
                    key: 'empty',
                    label: (
                        <Empty
                            description='Không có thông báo'
                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                            style={{ margin: '20px 0' }}
                        />
                    )
                }
            ];
        }

        const displayNotifications = notifications.slice(
            0,
            MAX_NOTIFICATIONS_DISPLAY
        );

        const markAllAsReadItem = {
            key: 'mark-all',
            label: (
                <div
                    style={{
                        fontSize: '12px',
                        textAlign: 'center',
                        padding: '2px 4px',
                        fontWeight: '500',
                        color: markingAllAsRead ? '#d9d9d9' : '#1890ff',
                        cursor: markingAllAsRead ? 'not-allowed' : 'pointer'
                    }}
                    onClick={(e) => {
                        e.stopPropagation();
                        if (!markingAllAsRead) {
                            handleMarkAllAsRead();
                        }
                    }}
                >
                    {markingAllAsRead ? (
                        <>
                            <Spin size='small' style={{ marginRight: '4px' }} />
                            Đang xử lý...
                        </>
                    ) : (
                        'Đánh dấu tất cả là đã đọc'
                    )}
                </div>
            )
        };

        const deleteAllNotificationsItem = {
            key: 'delete-all',
            label: (
                <div
                    style={{
                        fontSize: '12px',
                        textAlign: 'center',
                        padding: '2px 4px',
                        fontWeight: '500',
                        color: deletingAllNotifications ? '#d9d9d9' : '#ff4d4f',
                        cursor: deletingAllNotifications
                            ? 'not-allowed'
                            : 'pointer'
                    }}
                    onClick={(e) => {
                        e.stopPropagation();
                        if (!deletingAllNotifications) {
                            handleDeleteAllNotifications();
                        }
                    }}
                >
                    {deletingAllNotifications ? (
                        <>
                            <Spin size='small' style={{ marginRight: '4px' }} />
                            Đang xử lý...
                        </>
                    ) : (
                        'Xóa tất cả thông báo'
                    )}
                </div>
            )
        };

        const notificationItems = displayNotifications.map((notification) => ({
            key: notification.id,
            label: (
                <div
                    className={`notification-item${
                        notification.isRead ? ' isRead' : ''
                    }`}
                    onClick={() =>
                        !notification.isRead &&
                        handleMarkAsRead(notification.id)
                    }
                    style={{
                        cursor: notification.isRead ? 'default' : 'pointer'
                    }}
                >
                    <div style={{ fontWeight: 'bold' }}>
                        {notification.title}
                    </div>
                    <div
                        style={{
                            color: 'rgba(0, 0, 0, 0.45)',
                            marginTop: '4px',
                            fontSize: '13px'
                        }}
                    >
                        {notification.content}
                    </div>
                    <Button
                        className='delete-btn'
                        type='text'
                        icon={<DeleteOutlined />}
                        onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteNotification(notification.id);
                        }}
                    />
                </div>
            )
        }));

        return [
            markAllAsReadItem,
            deleteAllNotificationsItem,
            ...notificationItems
        ];
    };

    const debouncedSearch = useCallback(
        debounce(async (value) => {
            if (!value || !selectedBoard) return;
            setSearchLoading(true);
            try {
                const res = await TaskService.searchTasks(value, selectedBoard);
                onSearchResult?.(res.data || []);
            } catch (error) {
                console.error('Lỗi tìm kiếm task:', error);
            } finally {
                setSearchLoading(false);
            }
        }, 500),
        [selectedBoard]
    );

    const handleInputChange = (e) => {
        const value = e.target.value;
        setSearchText(value);
        if (value.trim()) {
            debouncedSearch(value);
        } else {
            debouncedSearch.cancel(); // Hủy bỏ debounce đang chạy
            onSearchResult?.(null);
        }
    };

    const handleSearch = () => {
        debouncedSearch.cancel(); // huỷ debounce nếu có
        if (searchText.trim()) {
            debouncedSearch(searchText);
        } else {
            onSearchResult?.(null);
        }
    };

    const handleClearSearch = () => {
        setSearchText('');
        debouncedSearch.cancel(); // Hủy bỏ debounce đang chạy
        onSearchResult?.(null);
    };

    return (
        <>
            {isLoggingOut && (
                <div className='logout-overlay'>
                    <div className='logout-spinner'>
                        <Spin size='large' />
                        <p>Đang đăng xuất...</p>
                    </div>
                </div>
            )}

            <Header className='dashboard-header'>
                <div className='header-left'>
                    <Button
                        type='text'
                        icon={
                            collapsed ? (
                                <MenuUnfoldOutlined />
                            ) : (
                                <MenuFoldOutlined />
                            )
                        }
                        onClick={onToggleCollapse}
                        className='collapse-btn'
                    />
                    <div className='logo'>
                        <img
                            src={'/gwtask.png'}
                            style={{ width: '24px', height: '24px' }}
                        />
                        <span className='logo-text'>GW Tasks</span>
                    </div>
                </div>

                <div className='header-center mobile-hidden'>
                    <div className='search-container'>
                        <Input
                            placeholder='Tìm kiếm task...'
                            prefix={
                                <SearchOutlined
                                    onClick={handleSearch}
                                    style={{ cursor: 'pointer' }}
                                />
                            }
                            suffix={
                                searchLoading ? (
                                    <Spin size='small' />
                                ) : searchText ? (
                                    <CloseCircleOutlined
                                        onClick={handleClearSearch}
                                        style={{
                                            cursor: 'pointer',
                                            color: 'gray'
                                        }}
                                    />
                                ) : null
                            }
                            className='search-input'
                            style={{ width: 300 }}
                            value={searchText}
                            onChange={handleInputChange}
                            onPressEnter={handleSearch}
                            allowClear={false}
                        />
                    </div>
                </div>

                <div className='header-right'>
                    <Dropdown
                        menu={{
                            items: getNotificationItems(),
                            style: {
                                maxHeight: '70vh',
                                overflow: 'auto',
                                width: 350,
                                padding: '8px 0'
                            }
                        }}
                        trigger={['click']}
                        open={notificationVisible}
                        onOpenChange={setNotificationVisible}
                        placement='bottomRight'
                    >
                        <Badge
                            count={
                                notifications.filter((n) => !n.isRead).length
                            }
                            style={{ marginRight: '20px' }}
                            overflowCount={99}
                        >
                            <Button
                                type='text'
                                icon={<BellOutlined />}
                                title='Thông báo'
                            />
                        </Badge>
                    </Dropdown>
                    <Dropdown
                        menu={{ items: userMenuItems }}
                        placement='bottomRight'
                        trigger={['click']}
                        open={userMenuVisible}
                        onOpenChange={setUserMenuVisible}
                    >
                        <Avatar
                            src={userInfo.photoUrl}
                            className='user-avatar'
                            style={{ cursor: 'pointer' }}
                            alt={`Ảnh đại diện của ${userInfo.name}`}
                            title='Nhấn để mở menu người dùng'
                        />
                    </Dropdown>
                </div>
            </Header>
        </>
    );
};

export default DashboardHeader;
