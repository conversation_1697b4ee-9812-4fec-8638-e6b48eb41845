// Utility để quản lý trạng thái workspace và board trong localStorage
const STATE_KEYS = {
    SELECTED_WORKSPACE: 'selectedWorkspace',
    SELECTED_BOARD: 'selectedBoard',
    WORKSPACE_BOARD_STATE: 'workspaceBoardState'
};

// Lưu workspace được chọn
export const saveSelectedWorkspace = (workspaceId) => {
    try {
        localStorage.setItem(STATE_KEYS.SELECTED_WORKSPACE, workspaceId?.toString() || '');
    } catch (error) {
        console.error('Lỗi khi lưu workspace:', error);
    }
};

// Lấy workspace được chọn
export const getSelectedWorkspace = () => {
    try {
        const workspaceId = localStorage.getItem(STATE_KEYS.SELECTED_WORKSPACE);
        return workspaceId ? parseInt(workspaceId) : null;
    } catch (error) {
        console.error('Lỗi khi lấy workspace:', error);
        return null;
    }
};

// Lưu board được chọn cho workspace cụ thể
export const saveSelectedBoard = (workspaceId, boardId) => {
    try {
        const stateKey = `${STATE_KEYS.SELECTED_BOARD}_${workspaceId}`;
        localStorage.setItem(stateKey, boardId?.toString() || '');
    } catch (error) {
        console.error('Lỗi khi lưu board:', error);
    }
};

// Lấy board được chọn cho workspace cụ thể
export const getSelectedBoard = (workspaceId) => {
    try {
        const stateKey = `${STATE_KEYS.SELECTED_BOARD}_${workspaceId}`;
        const boardId = localStorage.getItem(stateKey);
        return boardId ? parseInt(boardId) : null;
    } catch (error) {
        console.error('Lỗi khi lấy board:', error);
        return null;
    }
};

// Lưu trạng thái workspace và board
export const saveWorkspaceBoardState = (workspaceId, boardId) => {
    try {
        saveSelectedWorkspace(workspaceId);
        if (workspaceId) {
            saveSelectedBoard(workspaceId, boardId);
        }
    } catch (error) {
        console.error('Lỗi khi lưu trạng thái workspace và board:', error);
    }
};

// Lấy trạng thái workspace và board
export const getWorkspaceBoardState = () => {
    try {
        const workspaceId = getSelectedWorkspace();
        const boardId = workspaceId ? getSelectedBoard(workspaceId) : null;
        
        return {
            workspaceId,
            boardId
        };
    } catch (error) {
        console.error('Lỗi khi lấy trạng thái workspace và board:', error);
        return {
            workspaceId: null,
            boardId: null
        };
    }
};

// Xóa trạng thái workspace và board
export const clearWorkspaceBoardState = () => {
    try {
        localStorage.removeItem(STATE_KEYS.SELECTED_WORKSPACE);
        
        // Xóa tất cả các board state cho các workspace
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
            if (key.startsWith(STATE_KEYS.SELECTED_BOARD)) {
                localStorage.removeItem(key);
            }
        });
    } catch (error) {
        console.error('Lỗi khi xóa trạng thái workspace và board:', error);
    }
};

// Xóa board state cho workspace cụ thể
export const clearBoardStateForWorkspace = (workspaceId) => {
    try {
        const stateKey = `${STATE_KEYS.SELECTED_BOARD}_${workspaceId}`;
        localStorage.removeItem(stateKey);
    } catch (error) {
        console.error('Lỗi khi xóa board state cho workspace:', error);
    }
}; 