const assignService = require("../services/taskAssignService");
const TaskService = require("../services/taskService");
const googleTaskService = require("../services/googleTaskService");

const addAssigneeToTask = async (req, res) => {
    try {
        const { taskId, email } = req.body;

        // 1. <PERSON><PERSON> người dùng vào task trong DB
        const assignee = await assignService.addAssigneeToTask(
            taskId,
            email
        );

        // 2. Lấy task hiện tại từ DB
        const task = await TaskService.getTask(taskId);

        // 3. <PERSON><PERSON><PERSON> ra tất cả email đã được gán vào task
        const assignees = await assignService.getTaskAssignees(taskId);

        // 4. Tạo ra notes mới
        const newNotes = `${task.notes} [Assign: ${assignees
            .map((assignee) => assignee.email)
            .join(", ")}]`;

        // 5. <PERSON><PERSON><PERSON> Google API để update task
        await googleTaskService.updateTask({
            taskId: taskId,
            taskFields: {
                notes: newNotes,
            },
            userId: req.user.id,
        });

        res.status(200).json(assignee);
    } catch (error) {
        console.error("Error adding assignee:", error);
        res.status(500).json({ message: error.message });
    }
};

const removeAssigneeFromTask = async (req, res) => {
    try {
        const { taskId, email } = req.body;
        const assignee = await assignService.removeAssigneeFromTask(
            taskId,
            email
        );

        // 2. Lấy task hiện tại từ DB
        const task = await TaskService.getTask(taskId);

        // 4. Lấy ra tất cả email đã được gán vào task
        const assignees = await assignService.getTaskAssignees(taskId);

        // 5. Tạo ra notes mới
        let newNotes = "";
        if (assignees.length > 0) {
            newNotes = `${task.notes} [Assign: ${assignees
                .map((assignee) => assignee.email)
                .join(", ")}]`;
        } else {
            newNotes = task.notes;
        }

        // 6. Gọi Google API để update task
        await googleTaskService.updateTask({
            taskId: taskId,
            taskFields: {
                notes: newNotes,
            },
            userId: req.user.id,
        });

        res.status(200).json(assignee);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getTaskAssignees = async (req, res) => {
    try {
        const { taskId } = req.params;
        const assignees = await assignService.getTaskAssignees(taskId);
        res.status(200).json(assignees);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const updateTaskAssignees = async (req, res) => {
    try {
        const { taskId, emails } = req.body;
        const assignees = await assignService.updateTaskAssignees(
            taskId,
            emails
        );
        res.status(200).json(assignees);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const clearTaskAssignees = async (req, res) => {
    try {
        const { taskId } = req.params;
        const assignees = await assignService.clearTaskAssignees(taskId);
        res.status(200).json(assignees);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getUserAssignedTasks = async (req, res) => {
    try {
        const userId = req.user.id;
        const assignees = await assignService.getUserAssignedTasks(userId);
        res.status(200).json(assignees);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

module.exports = {
    addAssigneeToTask,
    removeAssigneeFromTask,
    getTaskAssignees,
    updateTaskAssignees,
    clearTaskAssignees,
    getUserAssignedTasks,
};
