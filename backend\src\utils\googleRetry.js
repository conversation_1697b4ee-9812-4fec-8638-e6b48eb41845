const pRetry = require('p-retry');
const redis = require('../utils/redisClient'); // Đ<PERSON><PERSON> bảo redis đã cấu hình đúng
const { v4: uuidv4 } = require('uuid'); // đ<PERSON> gán ID riêng cho fallback

function shouldRetryGoogleError(error) {
  const status = error?.response?.status;
  return (
    !status ||                // Lỗi mạng không có status
    status >= 500 ||          // Lỗi server
    status === 429 ||         // Quá giới hạn
    error.code === 'ECONNABORTED'
  );
}

// Ghi log fallback vào Redis
async function fallbackHandler(context, error, fallbackData = {}) {
  const fallbackKey = `google:sync:fallback`;
  const entry = {
    id: uuidv4(),
    context,
    error: error.message,
    data: fallbackData,
    timestamp: new Date().toISOString()
  };
  try {
    await redis.lpush(fallbackKey, JSON.stringify(entry));
    console.warn(`Fallback - thất bại khi đồng bộ ${context}, đã lưu vào <PERSON>`);
  } catch (redisError) {
    console.error('Ghi fallback vào Redis thất bại:', redisError.message);
  }

  return { fallback: true, error: error.message };
}

// Hàm retry chung cho mọi lệnh gọi Google API
async function retryGoogleCall(fn, context, fallbackData = {}) {
  try {
    return await pRetry(fn, {
      retries: 3,
      onFailedAttempt: error => {
        console.warn(`Retry thất bại lần ${error.attemptNumber} (${context}): ${error.message}`);
      },
      retryFailedTest: shouldRetryGoogleError
    });
  } catch (error) {
    return fallbackHandler(context, error, fallbackData);
  }
}

async function showFallbacks() {
  const results = await redis.lrange('google:sync:fallback', 0, -1);
  console.log('Dữ liệu fallback trong Redis:');
  results.forEach((entry, idx) => {
    console.log(`\nEntry ${idx + 1}:`, JSON.parse(entry));
  });
  redis.quit();
}

module.exports = { retryGoogleCall };
