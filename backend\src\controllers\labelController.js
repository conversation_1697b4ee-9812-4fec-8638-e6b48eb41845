const labelService = require("../services/labelService");

//Manage label
const createLabel = async (req, res) => {
  try {
    const { boardId, name, color } = req.body;
    const label = await labelService.createLabel(boardId, name, color);
    res.status(201).json(label);
  } catch (error) {
    console.log("Error creating label:", error);
    res.status(500).json({ message: error.message });
  }
};

const getLabelsByBoardId = async (req, res) => {
  try {
    const { boardId } = req.params;
    const labels = await labelService.getLabelsByBoardId(boardId);
    res.status(200).json(labels);
  } catch (error) {
    console.log("Error getting labels:", error);
    res.status(500).json({ message: error.message });
  }
};

const getLabelById = async (req, res) => {
  try {
    const { labelId } = req.params;
    const label = await labelService.getLabelById(labelId);
    res.status(200).json(label);
  } catch (error) {
    console.log("Error getting label:", error);
    res.status(500).json({ message: error.message });
  }
};

const updateLabel = async (req, res) => {
  try {
    const { labelId } = req.params;
    const { name, color } = req.body;
    const result = await labelService.updateLabel(labelId, name, color);
    res.status(200).json(result);
  } catch (error) {
    console.log("Error updating label:", error);
    res.status(500).json({ message: error.message });
  }
};

const deleteLabel = async (req, res) => {
  try {
    const { labelId } = req.params;
    const result = await labelService.deleteLabel(labelId);
    res.status(200).json(result);
  } catch (error) {
    console.log("Error deleting label:", error);
    res.status(500).json({ message: error.message });
  }
};

//Manage Task Labels
const addLabelToTask = async (req, res) => {
  try {
    const { taskId, labelId } = req.body;
    const result = await labelService.addLabelToTask(taskId, labelId);
    res.status(200).json(result);
  } catch (error) {
    console.log("Error adding label to task:", error);
    res.status(500).json({ message: error.message });
  }
};

const removeLabelFromTask = async (req, res) => {
  try {
    const { taskId, labelId } = req.body;
    const result = await labelService.removeLabelFromTask(taskId, labelId);
    res.status(200).json(result);
  } catch (error) {
    console.log("Error removing label from task:", error);
    res.status(500).json({ message: error.message });
  }
};

const getLabelsByTaskId = async (req, res) => {
  try {
    const { taskId } = req.params;
    const labels = await labelService.getLabelsByTaskId(taskId);
    res.status(200).json(labels);
  } catch (error) {
    console.log("Error getting labels by task id:", error);
    res.status(500).json({ message: error.message });
  }
};

module.exports = {
  createLabel,
  getLabelsByBoardId,
  getLabelById,
  updateLabel,
  deleteLabel,
  addLabelToTask,
  removeLabelFromTask,
  getLabelsByTaskId,
};
