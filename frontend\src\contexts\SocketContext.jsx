import { createContext, useContext, useEffect, useState } from "react";
import { io } from "socket.io-client";

// Tạo context
const SocketContext = createContext(null);

// Hook để sử dụng context
export const useSocket = () => useContext(SocketContext);

// Provider component
export const SocketProvider = ({ children }) => {
    const [socket, setSocket] = useState(null);
    const [connected, setConnected] = useState(false);

    useEffect(() => {
        // Khởi tạo kết nối socket
        let socketUrl = import.meta.env.VITE_SOCKET_URL;

        // Tự động detect protocol cho production
        if (import.meta.env.PROD) {
            // Nếu đang ở production và không có protocol, tự động thêm https
            if (socketUrl && !socketUrl.startsWith("http")) {
                socketUrl = `https://${socketUrl}`;
            }
            // Đ<PERSON><PERSON> bảo sử dụng HTTPS cho production
            if (socketUrl && socketUrl.startsWith("http://")) {
                socketUrl = socketUrl.replace("http://", "https://");
            }
        }

        //console.log('Đang kết nối Socket.IO đến:', socketUrl);

        const newSocket = io(socketUrl, {
            withCredentials: true,
            autoConnect: true,
            reconnection: true,
            reconnectionDelay: 1000,
            reconnectionAttempts: 10, // Tăng số lần thử kết nối lại
            // Cấu hình cho CloudLinux Passenger - ưu tiên polling trước
            transports: ["polling", "websocket"],
            timeout: 20000, // Tăng timeout
            // Thêm cấu hình cho production
            path: "/socket.io/",
            // Cho phép upgrade từ polling lên websocket
            upgrade: true,
            // Tăng ping interval cho Passenger
            pingInterval: 25000,
            pingTimeout: 60000,
            // Cấu hình cho WSS
            secure: import.meta.env.PROD,
            // Thêm forceNew để tránh cache connection
            forceNew: true,
        });

        // Xử lý các sự kiện kết nối
        newSocket.on("connect", () => {
            //console.log('Socket.IO kết nối thành công!');
            // console.log(
            //     'Transport method:',
            //     newSocket.io.engine.transport.name
            // );
            // console.log('Protocol:', window.location.protocol);
            setConnected(true);
        });

        newSocket.on("connect_error", (error) => {
            console.error("Socket.IO lỗi kết nối:", error);
            console.error("Chi tiết lỗi:", error.message);
            console.error(
                "Transport method:",
                newSocket.io.engine.transport.name
            );
            console.error("URL đang kết nối:", socketUrl);
            setConnected(false);
        });

        newSocket.on("disconnect", (reason) => {
            // console.log('Socket.IO ngắt kết nối:', reason);
            setConnected(false);
        });

        // Thêm event listener cho transport upgrade
        newSocket.on("upgrade", () => {
            // console.log(
            //     'Socket.IO upgraded to:',
            //     newSocket.io.engine.transport.name
            // );
        });

        // Lưu trữ tham chiếu socket
        setSocket(newSocket);

        // Hàm cleanup khi component unmount
        return () => {
            if (newSocket) {
                newSocket.off("connect");
                newSocket.off("connect_error");
                newSocket.off("disconnect");
                newSocket.off("upgrade");
                newSocket.off("workspace:member_removed");
                newSocket.close();
            }
        };
    }, []);

    // Workspace room handlers
    const joinWorkspace = (workspaceId) => {
        if (socket && connected) {
            socket.emit("workspace:join", workspaceId);
        }
    };

    const leaveWorkspace = (workspaceId) => {
        if (socket && connected) {
            socket.emit("workspace:leave", workspaceId);
        }
    };

    const emitAssignWorkspace = (data) => {
        if (socket && connected) {
            socket.emit("workspace:assigned", data);
        }
    };

    const emitUnassignWorkspace = (data) => {
        if (socket && connected) {
            socket.emit("workspace:unassigned", data);
        }
    };

    // Board room handlers
    const joinBoard = (boardId) => {
        if (socket && connected) {
            socket.emit("board:join", boardId);
        }
    };

    const leaveBoard = (boardId) => {
        if (socket && connected) {
            socket.emit("board:leave", boardId);
        }
    };
    const emitUpdateBoard = (data) => {
        if (socket && connected) {
            socket.emit("board:updated", data);
        }
    };

    const emitDeleteBoard = (data) => {
        if (socket && connected) {
            socket.emit("board:deleted", data);
        }
    };

    const emitAssignBoard = (data) => {
        if (socket && connected) {
            socket.emit("board:assigned", data);
        }
    };

    const emitUnassignBoard = (data) => {
        if (socket && connected) {
            socket.emit("board:unassigned", data);
        }
    };

    // Task event emitters
    const emitTaskCreated = (data) => {
        if (socket && connected) {
            socket.emit("task:created", data);
        }
    };

    const emitTaskUpdated = (data) => {
        if (socket && connected) {
            socket.emit("task:updated", data);
        }
    };

    const emitTaskDeleted = (data) => {
        if (socket && connected) {
            socket.emit("task:deleted", data);
        }
    };

    const emitTaskCompleted = (data) => {
        if (socket && connected) {
            socket.emit("task:completed", data);
        }
    };

    const emitTaskMoved = (data) => {
        if (socket && connected) {
            socket.emit("task:moved", data);
        }
    };

    const emitTaskAssigned = (data) => {
        if (socket && connected) {
            socket.emit("task:assigned", data);
        }
    };

    const emitTaskUnassigned = (data) => {
        if (socket && connected) {
            socket.emit("task:unassigned", data);
        }
    };

    // TaskList event emitters
    const emitTaskListCreated = (data) => {
        if (socket && connected) {
            socket.emit("taskList:created", data);
        }
    };

    const emitTaskListUpdated = (data) => {
        if (socket && connected) {
            socket.emit("taskList:updated", data);
        }
    };

    const emitTaskListDeleted = (data) => {
        if (socket && connected) {
            socket.emit("taskList:deleted", data);
        }
    };

    const emitTaskListMoved = (data) => {
        if (socket && connected) {
            socket.emit("taskList:moved", data);
        }
    };

    // Notification event emitters
    const emitNotificationCreated = (data) => {
        if (socket && connected) {
            socket.emit("notification:created", data);
        }
    };

    const emitNotificationUpdated = (data) => {
        if (socket && connected) {
            socket.emit("notification:updated", data);
        }
    };

    return (
        <SocketContext.Provider
            value={{
                socket,
                connected,
                // Workspace methods
                joinWorkspace,
                leaveWorkspace,
                emitAssignWorkspace,
                emitUnassignWorkspace,
                // Board methods
                joinBoard,
                leaveBoard,
                emitUpdateBoard,
                emitDeleteBoard,
                emitAssignBoard,
                emitUnassignBoard,
                // Task methods
                emitTaskCreated,
                emitTaskUpdated,
                emitTaskDeleted,
                emitTaskCompleted,
                emitTaskMoved,
                emitTaskAssigned,
                emitTaskUnassigned,
                // TaskList methods
                emitTaskListCreated,
                emitTaskListUpdated,
                emitTaskListDeleted,
                emitTaskListMoved,
                // Notification methods
                emitNotificationCreated,
                emitNotificationUpdated,
            }}
        >
            {children}
        </SocketContext.Provider>
    );
};

export default SocketProvider;
