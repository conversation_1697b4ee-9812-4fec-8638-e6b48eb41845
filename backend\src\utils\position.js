const MAX_LENGTH = 20;

function formatPosition(num) {
  return num.toString().padStart(MAX_LENGTH, '0');
}

// Tạo position tiếp theo (ví dụ sau position cuối)
function getNextPosition(lastPosition) {
  const last = parseInt(lastPosition || '0', 10);
  const next = last + 1;
  return formatPosition(next);
}

// Tạo position giữa 2 cái
function getMiddlePosition(posA, posB) {
  const a = parseInt(posA, 10);
  const b = parseInt(posB, 10);
  const mid = Math.floor((a + b) / 2);
  return formatPosition(mid);
}

module.exports = {
  getNextPosition,
  getMiddlePosition,
  formatPosition
};
