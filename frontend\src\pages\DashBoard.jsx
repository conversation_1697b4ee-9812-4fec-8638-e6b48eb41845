import React, { useState, useEffect } from "react";
import { Button, Avatar, Popover, Spin, Empty, message } from "antd";
import { useOutletContext } from "react-router-dom";
import {
  PlusOutlined,
  ExportOutlined,
  CloseOutlined,
  SyncOutlined,
  TagOutlined,
} from "@ant-design/icons";

// Import components
import TaskList from "../components/TaskList/TaskList";
import AddTaskListCard from "../components/TaskList/AddTaskListCard";
import TaskCard from "../components/Task/TaskCard";

// Import hooks
import useTaskList from "../hooks/useTaskList";
import useDragAndDrop from "../hooks/useDragAndDrop";

//Import service
import { syncEntireBoard } from "../services/asyncService";

// Import styles
import "./DashBoard.css";

import {
  closestCorners,
  DndContext,
  PointerSensor,
  TouchSensor,
  useSensor,
  useSensors,
  DragOverlay,
  defaultDropAnimationSideEffects,
} from "@dnd-kit/core";
import {
  arrayMove,
  horizontalListSortingStrategy,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";

const DashBoard = () => {
  const [assigneePopoverVisible, setAssigneePopoverVisible] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [isFetchingMembers, setIsFetchingMembers] = useState(false);
  const [refreshLabelsSignal, setRefreshLabelsSignal] = useState(0); // State để trigger refresh labels

  // Lấy context từ MainLayout thông qua Outlet
  const {
    selectedWorkspace,
    selectedBoard,
    workspaceMembers,
    boardAssignees,
    workspaces,
    boards,
    setSelectedWorkspace,
    setSelectedBoard,
    handleAddBoardAssignee,
    handleRemoveBoardAssignee,
    fetchWorkspaceMembers,
    refreshBoards,
    searchResult,
    setSearchResult,
    searchText,
    setSearchText,
  } = useOutletContext();

  //console.log(searchResult);

  // Lấy thông tin workspace hiện tại để kiểm tra quyền
  const currentWorkspace = workspaces.find((ws) => ws.id === selectedWorkspace);
  const isUserWorkspaceOwner = currentWorkspace?.isOwned || false;

  // Fetch workspace members khi selectedWorkspace thay đổi
  useEffect(() => {
    if (selectedWorkspace) {
      setIsFetchingMembers(true);
      fetchWorkspaceMembers(selectedWorkspace).finally(() => {
        setIsFetchingMembers(false);
      });
    }
  }, [selectedWorkspace, fetchWorkspaceMembers]);

  // Hàm cắt ngắn văn bản
  const truncateText = (text, maxLength = 40) => {
    if (!text) return "";
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  const {
    taskLists: taskListsFromHook,
    taskListLoading: taskListLoadingFromHook,
    isExporting: isExportingFromHook,
    setTaskLists: setTaskListsFromHook,
    addNewTaskList: addNewTaskListFromHook,
    deleteTaskList: deleteTaskListFromHook,
    updateTaskList: updateTaskListFromHook,
    exportBoardToSheet: exportBoardToSheetFromHook,
    fetchTaskLists: fetchTaskListsFromHook,
  } = useTaskList(selectedBoard, refreshBoards);

  const {
    activeDragType: activeDragTypeFromHook,
    activeDragId: activeDragIdFromHook,
    activeDragData: activeDragDataFromHook,
    isAnyModalOpen: isAnyModalOpenFromHook,
    setIsAnyModalOpen: setIsAnyModalOpenFromHook,
    onDragStart: onDragStartFromHook,
    onDragOver: onDragOverFromHook,
    onDragEnd: onDragEndFromHook,
  } = useDragAndDrop(
    taskListsFromHook,
    setTaskListsFromHook,
    selectedBoard,
    refreshBoards
  );

  // Sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5,
      },
    })
  );

  const dropAnimation = {
    sideEffects: defaultDropAnimationSideEffects({
      style: {
        active: {
          opacity: 0.5,
        },
      },
    }),
  };

  // Function để trigger refresh labels cho tất cả TaskCard
  const handleRefreshLabels = () => {
    setRefreshLabelsSignal((prev) => prev + 1);
  };

  const handleAsyncGoogleTask = async () => {
    try {
      setIsSyncing(true);
      message.loading({
        content: "Đang đồng bộ với Google Tasks...",
        key: "sync",
      });
      await syncEntireBoard(selectedBoard);
      message.success({ content: "Đồng bộ thành công!", key: "sync" });
      // Refresh lại task lists và boards sau khi đồng bộ
      if (selectedBoard) {
        await fetchTaskListsFromHook();
        if (refreshBoards) {
          await refreshBoards();
        }
      }
    } catch (error) {
      message.error({
        content: error.message || "Đồng bộ thất bại. Vui lòng thử lại sau.",
        key: "sync",
      });
    } finally {
      setIsSyncing(false);
    }
  };

  const assigneeContent = (
    <div style={{ width: 280 }}>
      <div
        style={{
          padding: "8px 0",
          borderBottom: "1px solid #f0f0f0",
          marginBottom: "8px",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <span style={{ fontWeight: 500 }}>Thành viên Workspace</span>
          <CloseOutlined
            onClick={() => setAssigneePopoverVisible(false)}
            style={{ cursor: "pointer" }}
          />
        </div>
      </div>
      <div
        style={{
          minHeight: "100px",
          maxHeight: "200px",
          overflowY: "auto",
        }}
      >
        {isFetchingMembers ? (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              padding: "20px",
            }}
          >
            <Spin size="small" />
          </div>
        ) : workspaceMembers.length === 0 ? (
          <Empty
            description="Không có thành viên trong workspace"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          workspaceMembers
            .filter((member) => {
              const memberId = member.user.id;
              return !boardAssignees.some((assignee) => {
                const assigneeUserId =
                  assignee.user?.id || assignee.User?.id || assignee.user_id;
                return assigneeUserId === memberId;
              });
            })
            .map((member) => {
              const user = member.user;
              return (
                <div
                  key={member.id}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    padding: "8px 0",
                    cursor: "pointer",
                    borderRadius: "4px",
                    transition: "background-color 0.2s",
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = "#f5f5f5";
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = "transparent";
                  }}
                  onClick={() => {
                    handleAddBoardAssignee(selectedBoard, user);
                    setAssigneePopoverVisible(false);
                  }}
                >
                  <Avatar
                    size={32}
                    src={user.photo_url || user.photoUrl}
                    style={{
                      backgroundColor: "#1890ff",
                      marginRight: "8px",
                    }}
                  >
                    {!user.photo_url &&
                      !user.photoUrl &&
                      (user.full_name || user.fullName || user.email || "U")
                        .charAt(0)
                        .toUpperCase()}
                  </Avatar>
                  <div>
                    <div style={{ fontWeight: 500 }}>
                      {user.full_name || user.fullName || user.email}
                    </div>
                    <div
                      style={{
                        fontSize: "12px",
                        color: "#8c8c8c",
                      }}
                    >
                      {user.email}
                    </div>
                  </div>
                </div>
              );
            })
        )}
      </div>
    </div>
  );

  const renderBoardAssignees = () => {
    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          gap: "6px",
          flexWrap: "wrap",
        }}
      >
        {boardAssignees && boardAssignees.length > 0 ? (
          <>
            {boardAssignees.map((assignee, index) => {
              const user = assignee.user;
              return (
                <div
                  key={user.id || index}
                  className="board-assignee-item"
                  title={`${user.full_name} (${user.email})`}
                >
                  <Avatar
                    size={28}
                    src={user.photo_url}
                    style={{
                      backgroundColor: "#1890ff",
                      border: "2px solid white",
                      boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                    }}
                  />
                  {isUserWorkspaceOwner && (
                    <div
                      className="remove-assignee-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveBoardAssignee(selectedBoard, assignee);
                      }}
                      title="Xóa thành viên"
                    >
                      ×
                    </div>
                  )}
                </div>
              );
            })}
          </>
        ) : (
          <span style={{ fontSize: "14px", color: "#8c8c8c" }}>
            Không có người tham gia
          </span>
        )}

        {isUserWorkspaceOwner && (
          <Popover
            content={assigneeContent}
            title={null}
            trigger="click"
            open={assigneePopoverVisible}
            onOpenChange={setAssigneePopoverVisible}
            placement="bottomLeft"
          >
            <Button
              type="text"
              icon={<PlusOutlined />}
              size="small"
              shape="circle"
              style={{
                width: "24px",
                height: "24px",
                minWidth: "unset",
                color: "#bfbfbf",
              }}
              onClick={(e) => {
                e.stopPropagation();
                setAssigneePopoverVisible(true);
              }}
              title="Thêm thành viên"
            />
          </Popover>
        )}
      </div>
    );
  };

  return (
    <>
      {!selectedBoard ? (
        <div className="empty-board-state">
          <Empty description="Vui lòng chọn một Board để bắt đầu làm việc" />
        </div>
      ) : (
        <>
          <div className="content-header">
            <div className="content-title">
              <h1 title={boards.find((b) => b.id === selectedBoard)?.name}>
                {truncateText(boards.find((b) => b.id === selectedBoard)?.name)}
              </h1>
              <div>{renderBoardAssignees()}</div>
            </div>
            <div className="content-actions">
              {isUserWorkspaceOwner && (
                <Button
                  type="primary"
                  icon={<SyncOutlined spin={isSyncing} />}
                  onClick={handleAsyncGoogleTask}
                  loading={isSyncing}
                  disabled={
                    boards.find((b) => b.id === selectedBoard)?.isGoogleSynced
                  }
                >
                  {isSyncing
                    ? "Đang đồng bộ..."
                    : boards.find((b) => b.id === selectedBoard)?.isGoogleSynced
                    ? "Đã đồng bộ"
                    : "Đồng bộ ngay"}
                </Button>
              )}
              <Button
                icon={<ExportOutlined />}
                type="primary"
                onClick={exportBoardToSheetFromHook}
                loading={isExportingFromHook}
              >
                Xuất sang Google Sheet
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={addNewTaskListFromHook}
              >
                Thêm Task list
              </Button>
            </div>
          </div>

          <div className="task-board">
            {taskListLoadingFromHook ? (
              <div
                style={{
                  position: "absolute",
                  top: "50%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  zIndex: 1000,
                }}
              >
                <Spin size="large" />
                <div
                  style={{
                    marginTop: "16px",
                    color: "rgba(0, 0, 0, 0.45)",
                  }}
                >
                  Đang tải Task list...
                </div>
              </div>
            ) : searchResult && searchResult.length > 0 ? (
              searchResult.map((taskList) => (
                <TaskList
                  boardId={selectedBoard}
                  workspaceId={selectedWorkspace}
                  boardMembers={boardAssignees}
                  key={taskList.id}
                  taskList={taskList}
                  onDeleteTaskList={deleteTaskListFromHook}
                  onUpdateTaskList={updateTaskListFromHook}
                  onModalStateChange={setIsAnyModalOpenFromHook}
                  refreshBoards={refreshBoards}
                  searchText={searchText}
                  setSearchResult={setSearchResult}
                  refreshLabelsSignal={refreshLabelsSignal} // Truyền refreshLabelsSignal
                  onRefreshAllLabels={handleRefreshLabels} // Truyền handleRefreshLabels
                />
              ))
            ) : (
              <DndContext
                collisionDetection={closestCorners}
                sensors={sensors}
                onDragStart={onDragStartFromHook}
                onDragOver={onDragOverFromHook}
                onDragEnd={onDragEndFromHook}
              >
                <SortableContext
                  items={taskListsFromHook.map((taskList) => taskList.id)}
                  strategy={horizontalListSortingStrategy}
                >
                  {taskListsFromHook.map((taskList) => (
                    <TaskList
                      boardId={selectedBoard}
                      workspaceId={selectedWorkspace}
                      boardMembers={boardAssignees}
                      key={taskList.id}
                      taskList={taskList}
                      onDeleteTaskList={deleteTaskListFromHook}
                      onUpdateTaskList={updateTaskListFromHook}
                      onModalStateChange={setIsAnyModalOpenFromHook}
                      refreshBoards={refreshBoards}
                      refreshLabelsSignal={refreshLabelsSignal} // Truyền refreshLabelsSignal
                      onRefreshAllLabels={handleRefreshLabels} // Truyền handleRefreshLabels
                    />
                  ))}
                </SortableContext>
                <DragOverlay dropAnimation={dropAnimation}>
                  {(!activeDragIdFromHook || !activeDragTypeFromHook) && null}
                  {activeDragTypeFromHook === "taskList" && (
                    <TaskList
                      key={activeDragIdFromHook}
                      boardId={selectedBoard}
                      workspaceId={selectedWorkspace}
                      taskList={activeDragDataFromHook}
                      refreshBoards={refreshBoards}
                    />
                  )}
                  {activeDragTypeFromHook === "task" && (
                    <>
                      <TaskCard
                        boardId={selectedBoard}
                        workspaceId={selectedWorkspace}
                        boardMembers={boardAssignees}
                        key={activeDragIdFromHook}
                        task={activeDragDataFromHook}
                        listId={activeDragDataFromHook.listId}
                        isParent={
                          activeDragDataFromHook.isParent ||
                          (activeDragDataFromHook.children &&
                            activeDragDataFromHook.children.length > 0)
                        }
                        childrenTasks={activeDragDataFromHook.children || []}
                      />

                      {(activeDragDataFromHook.isParent ||
                        (activeDragDataFromHook.children &&
                          activeDragDataFromHook.children.length > 0)) &&
                        activeDragDataFromHook.children &&
                        activeDragDataFromHook.children.map(
                          (childTask, index) => (
                            <div
                              key={`child-${childTask.id}`}
                              style={{
                                opacity: 0.6,
                                marginLeft: "20px",
                                marginTop: index === 0 ? "2px" : "0px",
                              }}
                            >
                              <TaskCard
                                boardId={selectedBoard}
                                workspaceId={selectedWorkspace}
                                boardMembers={boardAssignees}
                                task={childTask}
                                listId={activeDragDataFromHook.listId}
                                isChildTask={true}
                              />
                            </div>
                          )
                        )}
                    </>
                  )}
                </DragOverlay>
              </DndContext>
            )}

            {!taskListLoadingFromHook && !searchResult && (
              <AddTaskListCard onAddTaskList={addNewTaskListFromHook} />
            )}
          </div>
        </>
      )}
    </>
  );
};

export default DashBoard;
