# Node modules
node_modules/

# Build output
dist/
build/
tmp/
temp/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.*.local

# OS files
.DS_Store
Thumbs.db

# IDE settings
.vscode/
.idea/

# Coverage directory
coverage/

# Dependency directories
jspm_packages/

# TypeScript
*.tsbuildinfo

# Optional npm cache directory
.npm/

# Optional eslint cache
.eslintcache

# Parcel-bundler cache
.cache/

# Next.js
.next/

# Nuxt.js
.nuxt/

# SvelteKit
.svelte-kit/

# Remix
.build/

# Local database
*.sqlite
*.sqlite3
*.db

# Misc
*.tgz
*.log.*
