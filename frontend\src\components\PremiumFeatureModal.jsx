import React from "react";
import { Mo<PERSON>, Typo<PERSON>, Button, Space } from "antd";
import "./PremiumFeatureModal.css";

const { Title, Text } = Typography;

const PremiumFeatureModal = ({
  visible,
  onClose,
  title = "Multiple boards is a Premium Feature",
  description = "Create multiple boards for each of your projects to keep everything organized.",
  onLearnMore,
  onTryPremium,
}) => {
  const handleLearnMore = () => {
    if (onLearnMore) {
      onLearnMore();
    } else {
      // Default action - có thể redirect đến trang premium info
      console.log("Learn more about Premium");
    }
  };

  const handleTryPremium = () => {
    if (onTryPremium) {
      onTryPremium();
    } else {
      // Default action - có thể redirect đến trang đăng ký premium
      console.log("Try Premium for free");
    }
  };

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      centered
      width={480}
      className="premium-feature-modal"
      closable={true}
    >
      <div className="premium-modal-content">
        {/* Header */}
        <div className="premium-modal-header">
          <Title level={3} className="premium-modal-title">
            {title}
          </Title>
          <Text className="premium-modal-description">{description}</Text>
        </div>

        {/* Illustration */}
        <div className="premium-modal-illustration">
          <div className="boards-stack">
            {/* Board 3 (back) */}
            <div className="board board-3">
              <div className="board-header">
                <div className="board-title">Secret Project</div>
                <div className="board-tabs">
                  <div className="tab"></div>
                  <div className="tab"></div>
                  <div className="tab"></div>
                </div>
              </div>
            </div>

            {/* Board 2 (middle) */}
            <div className="board board-2">
              <div className="board-header">
                <div className="board-title">Personal Tasks</div>
                <div className="board-tabs">
                  <div className="tab"></div>
                  <div className="tab"></div>
                  <div className="tab"></div>
                </div>
              </div>
              <div className="board-content">
                <div className="task-column">
                  <div className="task-item"></div>
                  <div className="task-item"></div>
                  <div className="task-item"></div>
                </div>
                <div className="task-column">
                  <div className="task-item"></div>
                  <div className="task-item"></div>
                </div>
                <div className="task-column">
                  <div className="task-item"></div>
                  <div className="task-item"></div>
                  <div className="task-item"></div>
                </div>
              </div>
            </div>

            {/* Board 1 (front) */}
            <div className="board board-1">
              <div className="board-header">
                <div className="board-title">Work Project</div>
                <div className="board-tabs">
                  <div className="tab"></div>
                  <div className="tab"></div>
                  <div className="tab"></div>
                </div>
              </div>
              <div className="board-content">
                <div className="task-column">
                  <div className="task-item"></div>
                  <div className="task-item"></div>
                  <div className="task-item"></div>
                  <div className="task-item"></div>
                </div>
                <div className="task-column">
                  <div className="task-item"></div>
                  <div className="task-item"></div>
                  <div className="task-item"></div>
                </div>
                <div className="task-column">
                  <div className="task-item"></div>
                  <div className="task-item"></div>
                </div>
              </div>
              {/* Background image overlay */}
              <div className="board-background"></div>
            </div>
          </div>
        </div>

        {/* Footer Actions */}
        <div className="premium-modal-footer">
          <Space
            size="middle"
            style={{ width: "100%", justifyContent: "center" }}
          >
            <Button
              type="text"
              onClick={handleLearnMore}
              className="learn-more-btn"
            >
              Learn more about Premium
            </Button>
            <Button
              type="primary"
              onClick={handleTryPremium}
              className="try-premium-btn"
              size="large"
            >
              Try Premium for free
            </Button>
          </Space>
        </div>
      </div>
    </Modal>
  );
};

export default PremiumFeatureModal;
