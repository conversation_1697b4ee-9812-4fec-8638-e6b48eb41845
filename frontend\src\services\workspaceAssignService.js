import axios from '../utils/axiosCustomize';


const assignUserToWorkspace = async (userId, workspaceId) => {
    const response = await axios.post(`/workspace-assign/assign`, { userId, workspaceId });
    return response.data;
}

const unassignUserFromWorkspace = async (userId, workspaceId) => {
    const response = await axios.post(`/workspace-assign/unassign`, { userId, workspaceId });
    return response.data;
}

const getWorkspaceAssignees = async (workspaceId) => {
    const response = await axios.get(`/workspace-assign/${workspaceId}`);
    return response.data;
}

const getUserAssignedWorkspaces = async (userId) => {
    const response = await axios.get(`/workspace-assign/user/${userId}`);
    return response.data;
}

const leaveAssignedWorkspace = async (workspaceId) => {
    const response = await axios.post(`/workspace-assign/leave`, {workspaceId });
    return response.data;
}

export default { assignUserToWorkspace, unassignUserFromWorkspace, getWorkspaceAssignees, getUserAssignedWorkspaces, leaveAssignedWorkspace };