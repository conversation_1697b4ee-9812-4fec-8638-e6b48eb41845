const taskListService = require("../services/taskListService");
const GoogleTaskService = require("../services/googleTaskListService");
const userService = require("../services/userService");
const boardService = require("../services/boardService");

// Lấy danh sách task lists từ database
const getTaskLists = async (req, res) => {
    try {
        const { boardId } = req.params;

        // Kiểm tra board có tồn tại và user có quyền không
        const board = await taskListService.getBoardForUser(
            boardId,
            req.user.id
        );

        if (!board) {
            return res.status(404).json({
                message: "Board không tồn tại hoặc bạn không có quyền truy cập",
            });
        }

        const dbTaskLists = await taskListService.getTaskLists(
            boardId,
            req.user.id
        );
        res.json(dbTaskLists);
    } catch (error) {
        console.error("Lỗi khi lấy task lists:", error);
        res.status(500).json({
            message: "Lỗi khi lấy task lists",
            error: error.message,
        });
    }
};

// Lấy tất cả danh sách việc cần làm của người dùng đã xác thực.
const getAllTaskLists = async (req, res) => {
    try {
        const defaultBoard = await taskListService.getDefaultBoard(req.user.id);

        if (!defaultBoard) {
            return res.status(404).json({
                message:
                    "Không tìm thấy board mặc định. Vui lòng đăng xuất và đăng nhập lại.",
            });
        }

        const taskLists = await taskListService.getAllTaskLists(req.user.id);
        res.json(taskLists);
    } catch (error) {
        console.error("Lỗi khi lấy tất cả danh sách việc cần làm:", error);
        res.status(500).json({
            message: "Lỗi khi lấy tất cả danh sách việc cần làm",
            error: error.message,
        });
    }
};

// Tạo task list mới
const createTaskList = async (req, res) => {
    try {
        const { boardId, taskList } = req.body;
        const { title } = taskList;

        // Kiểm tra các điều kiện đầu vào
        if (!title) {
            return res.status(400).json({ message: "Tiêu đề là bắt buộc" });
        }

        //Lấy thông tin của user creator từ board
        const board = await boardService.getBoardById(boardId);
        const user = await userService.getUserById(board.createdBy);

        await taskListService.markBoardForSync(boardId);
        const responseData = await taskListService.createTaskList(
            boardId,
            user.id,
            taskList
        );

        // Gọi API đồng bộ với Google Task
        await GoogleTaskService.createTaskList({
            taskList: responseData,
            userId: user.id,
        });

        res.status(201).json(responseData);
    } catch (error) {
        console.error("Lỗi khi tạo task list:", error);
        res.status(error.code).json({
            message: "Lỗi khi tạo task list",
            error: error.message,
        });
    }
};

// Cập nhật task list
const updateTaskList = async (req, res) => {
    try {
        const { taskListId } = req.params;
        const { title, boardId, description } = req.body;

        if (!title) {
            return res.status(400).json({ message: "Tiêu đề là bắt buộc" });
        }

        const board = await boardService.getBoardById(boardId);
        const user = await userService.getUserById(board.createdBy);

        await taskListService.markBoardForSync(boardId);
        const updatedTaskList = await taskListService.updateTaskList(
            taskListId,
            { title, description }
        );

        // Gọi API đồng bộ với Google Task
        await GoogleTaskService.updateTaskList({
            taskList: updatedTaskList,
            userId: user.id,
        });

        res.json(updatedTaskList);
    } catch (error) {
        console.error("Lỗi khi cập nhật task list:", error);
        res.status(error.code).json({
            message: "Lỗi khi cập nhật task list",
            error: error.message,
        });
    }
};

// Xóa task list
const deleteTaskList = async (req, res) => {
    try {
        const { taskListId } = req.params;
        const boardId = req.body.boardId;

        const board = await boardService.getBoardById(boardId);
        const user = await userService.getUserById(board.createdBy);

        await taskListService.markBoardForSync(boardId);

        // Gọi API đồng bộ với Google Task
        await GoogleTaskService.deleteTaskList({
            taskListId: taskListId,
            userId: user.id,
        });

        await taskListService.deleteTaskList(taskListId);

        res.status(204).send({ message: "Success" });
    } catch (error) {
        console.error("Lỗi khi xóa task list:", error);
        res.status(error.code).json({
            message: "Lỗi khi xóa task list",
            error: error.message,
        });
    }
};

const getTaskListByBoardId = async (req, res) => {
    try {
        const { boardId } = req.params;

        const taskLists = await taskListService.getTaskListByBoardId(boardId);
        res.status(200).json(taskLists);
    } catch (error) {
        console.error("Lỗi khi lấy task list theo boardId:", error);
        res.status(500).json({
            message: "Lỗi khi lấy task list theo boardId",
            error: error.message,
        });
    }
};

const updateTaskListsOrder = async (req, res) => {
    try {
        const { boardId, taskListIds } = req.body;

        await taskListService.markBoardForSync(boardId);
        await taskListService.updateTaskListsOrder(boardId, taskListIds);

        res.status(200).json({ message: "Thứ tự task list đã được cập nhật" });
    } catch (error) {
        console.error("Lỗi khi cập nhật thứ tự task list:", error);
        res.status(500).json({
            message: "Lỗi khi cập nhật thứ tự task list",
            error: error.message,
        });
    }
};

// Di chuyển task list
const moveTaskList = async (req, res) => {
    try {
        const { taskListId } = req.params;
        const { previousTaskListId, nextTaskListId, boardId } = req.body;

        await taskListService.markBoardForSync(boardId);
        const movedTaskList = await taskListService.moveTaskList(
            taskListId,
            previousTaskListId,
            nextTaskListId
        );

        res.json(movedTaskList);
    } catch (error) {
        console.error("Lỗi khi di chuyển task list:", error);
        res.status(500).json({
            message: "Lỗi khi di chuyển task list",
            error: error.message,
        });
    }
};

module.exports = {
    getTaskLists,
    getAllTaskLists,
    createTaskList,
    updateTaskList,
    deleteTaskList,
    getTaskListByBoardId,
    updateTaskListsOrder,
    moveTaskList,
};
