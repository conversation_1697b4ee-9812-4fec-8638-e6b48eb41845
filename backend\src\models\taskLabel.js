module.exports = (sequelize, DataTypes) => {
    const TaskLabel = sequelize.define(
        "TaskLabel",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
            },
            taskId: {
                type: DataTypes.STRING(255),
                allowNull: false,
                field: "task_id",
            },
            labelId: {
                type: DataTypes.UUID,
                allowNull: false,
                field: "label_id",
            },
        },
        {
            tableName: "task_labels",
            timestamps: false,
        }
    );

    TaskLabel.associate = function (models) {
        TaskLabel.belongsTo(models.Task, {
            foreignKey: "task_id",
            as: "tasks",
        });
        TaskLabel.belongsTo(models.Label, {
            foreignKey: "label_id",
            as: "labels",
        });
    };

    return TaskLabel;
};
