/* Label Badge Styles */
.label-badge {
    font-size: 12px !important;
    line-height: 1.2 !important;
    padding: 2px 6px !important;
    border-radius: 10px !important;
    margin: 1px !important;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
}

.label-badge:hover {
    opacity: 0.8;
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

/* Label Selector Popover */
.label-selector-popover .ant-popover-content {
    padding: 0;
}

.label-selector-popover .ant-popover-inner {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.label-selector-popover .ant-list-item {
    padding: 8px 12px !important;
    border-radius: 4px;
    margin: 2px 0;
    transition: background-color 0.2s ease;
}

/* Emoji Picker Styles */
.emoji-picker-popover .ant-popover-content {
    padding: 0;
}

.emoji-picker-popover .ant-popover-inner {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.emoji-picker-content {
    padding: 12px;
    max-width: 220px;
}

.emoji-picker-trigger {
    min-width: 40px;
    width: 40px;
    height: 32px;
    justify-content: center !important;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 0 8px;
}

.emoji-picker-trigger:hover {
    border-color: #1890ff;
    color: #1890ff;
}

.emoji-picker-trigger:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 4px;
    max-width: 200px;
}

.emoji-button {
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
}

.emoji-button:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.emoji-button.selected {
    background-color: #1890ff !important;
    border-color: #1890ff !important;
    color: white !important;
    transform: scale(1.05);
}

.label-selector-popover .ant-list-item:hover {
    background-color: #f5f5f5;
}

/* Task Labels Section */
.task-labels {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-wrap: wrap;
    min-height: 24px;
}

.task-labels .label-badge {
    margin: 0 2px 2px 0;
}

/* Label Manager Modal */
.ant-modal .ant-form-item-label > label {
    font-weight: 500;
}

/* Color Picker in Label Manager */
.ant-color-picker-trigger {
    border-radius: 4px !important;
}

/* Label list in manager */
.ant-list-item-action {
    margin-left: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .label-badge {
        font-size: 11px !important;
        padding: 1px 4px !important;
        max-width: 80px;
    }

    .task-labels {
        gap: 2px;
    }
}

/* Animation for label changes */
.label-badge {
    animation: labelFadeIn 0.3s ease-in-out;
}

@keyframes labelFadeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Label selector button styles */
.task-labels .ant-btn {
    border: none;
    box-shadow: none;
    padding: 0;
    height: auto;
    line-height: 1;
}

.task-labels .ant-btn:hover {
    background-color: #f0f0f0;
    border-radius: 3px;
}

/* Empty state in label selector */
.ant-empty-small {
    margin: 8px 0;
}

/* Search input in label selector */
.label-selector-popover .ant-input {
    border-radius: 6px;
}

/* Label manager form styles */
.ant-form-item-label {
    padding-bottom: 4px;
}

.ant-color-picker {
    width: auto;
}

/* Divider in label selector */
.label-selector-popover .ant-divider {
    margin: 8px 0;
}

/* Button group in label selector footer */
.label-selector-popover .ant-space {
    width: 100%;
}

/* Checkbox in label selector */
.label-selector-popover .ant-checkbox-wrapper {
    margin-right: 8px;
}

/* Label item in selector */
.label-selector-popover .ant-list-item > div {
    width: 100%;
    display: flex;
    align-items: center;
}

/* Hover effect for label items */
.label-selector-popover .ant-list-item:hover .label-badge {
    transform: none;
}

/* Close icon in label badge */
.label-badge .anticon-close {
    font-size: 10px;
    margin-left: 4px;
    opacity: 0.7;
}

.label-badge .anticon-close:hover {
    opacity: 1;
}

/* Task card label section */
.task-card .task-labels {
    margin: 4px 0 8px 0;
    min-height: 20px;
}

/* Label section in task edit modal */
.ant-modal .task-labels {
    margin: 0;
}

/* Compact label display in cards */
.task-card .label-badge {
    font-size: 12px !important;
    padding: 2px 6px !important;
    margin: 0 2px 2px 0 !important;
}

/* Label manager list item actions */
.ant-list-item-action > li {
    padding: 0 4px;
}

.ant-list-item-action .ant-btn {
    padding: 4px;
    height: auto;
    width: auto;
    min-width: auto;
}

/* Color picker in create form */
.ant-form-inline .ant-form-item {
    margin-right: 8px;
}

.ant-form-inline .ant-form-item:last-child {
    margin-right: 0;
}

/* Loading state */
.label-selector-popover .ant-spin {
    display: block;
    text-align: center;
}

/* Success/Error states */
.label-badge.success {
    animation: labelSuccess 0.5s ease-in-out;
}

@keyframes labelSuccess {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Focus states */
.label-selector-popover .ant-input:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Disabled states */
.label-badge.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .label-badge {
        border: 2px solid currentColor !important;
        font-weight: bold;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .label-badge,
    .label-badge:hover {
        animation: none;
        transition: none;
        transform: none;
    }
}
