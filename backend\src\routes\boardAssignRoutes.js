const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../middleware/auth');
const { handleTokenExpiration } = require('../middleware/googleAuth');
const { addAssigneeToBoard, removeAssigneeFromBoard, getBoardAssignees, getUserBoards } = require('../controllers/boardAssignController');

router.post('/add-assignee', isAuthenticated, handleTokenExpiration, addAssigneeToBoard);
router.post('/remove-assignee', isAuthenticated, handleTokenExpiration, removeAssigneeFromBoard);
router.get('/user-boards', isAuthenticated, handleTokenExpiration, getUserBoards);
router.get('/:boardId', isAuthenticated, handleTokenExpiration, getBoardAssignees);

module.exports = router;