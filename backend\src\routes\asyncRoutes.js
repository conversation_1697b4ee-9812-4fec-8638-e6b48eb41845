const express = require('express');
const router = express.Router();
const asyncController = require('../controllers/asyncController');
const { isAuthenticated } = require('../middleware/auth');
const { handleTokenExpiration } = require('../middleware/googleAuth');

// Đồng bộ một task list
router.post('/tasklist/:taskListId/sync', isAuthenticated, handleTokenExpiration, asyncController.syncTaskList);

// Đồng bộ một task
router.post('/task/:taskId/sync', isAuthenticated, handleTokenExpiration, asyncController.syncTask);

// Đồng bộ tất cả tasks trong một task list
router.post('/tasklist/:taskListId/sync-all-tasks', isAuthenticated, handleTokenExpiration, asyncController.syncAllTasksInList);

// <PERSON>ồng bộ toàn bộ board
router.post('/board/:boardId/sync', isAuthenticated, handleTokenExpiration, asyncController.syncEntireBoard);


module.exports = router;
