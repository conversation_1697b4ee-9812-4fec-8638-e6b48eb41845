import React from 'react';
import { Card } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

const AddTaskListCard = ({ onAddTaskList }) => {
    return (
        <div className='add-tasklist-column'>
            <Card
                className='add-tasklist-card'
                size='small'
                hoverable
                onClick={onAddTaskList}
            >
                <div className='add-tasklist-content'>
                    <PlusOutlined
                        style={{ fontSize: '24px', color: '#8c8c8c' }}
                    />
                    <span className='add-tasklist-text'>
                        Thêm task list mới
                    </span>
                </div>
            </Card>
        </div>
    );
};

export default AddTaskListCard;
