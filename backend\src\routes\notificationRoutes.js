const express = require('express');
const router = express.Router();
const notificationController = require('../controllers/notificationController');
const { isAuthenticated } = require('../middleware/auth');
const { handleTokenExpiration } = require('../middleware/googleAuth');

// L<PERSON><PERSON> danh sách thông báo của user hiện tại
router.get('/', isAuthenticated, handleTokenExpiration, notificationController.getNotifications);

// Xóa một thông báo
router.delete('/:notificationId', isAuthenticated, handleTokenExpiration, notificationController.deleteNotification);

// Đ<PERSON>h dấu một thông báo đã đọc
router.put('/:notificationId/read', isAuthenticated, handleTokenExpiration, notificationController.markAsRead);

// Đ<PERSON><PERSON> d<PERSON>u tất cả thông báo đã đọc
router.put('/read', isAuthenticated, handleTokenExpiration, notificationController.markAllAsRead);

// Xóa tất cả thông báo
router.delete('/', isAuthenticated, handleTokenExpiration, notificationController.deleteAllNotifications);  

module.exports = router; 