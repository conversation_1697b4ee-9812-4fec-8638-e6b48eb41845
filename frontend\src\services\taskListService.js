import axios from '../utils/axiosCustomize';

const getTaskLists = async () => {
        const response = await axios.get('/tasklists');
        return response.data;
}

const getAllTaskLists = async () => {
        const response = await axios.get('/tasklists/all');
        return response.data;
}

const createTaskList = async (boardId, taskList) => {
        const response = await axios.post(`/tasklists`, { boardId, taskList });
        return response.data;
}

const updateTaskList = async (taskListId, taskList) => {
        const response = await axios.put(`/tasklists/${taskListId}`, taskList);
        return response.data;
}

const deleteTaskList = async (boardId, taskListId) => {
        const response = await axios.delete(`/tasklists/${taskListId}`, {
            data: {
                boardId: boardId
            }
        });
        return response.data;
}

const getTaskListByBoardId = async (boardId) => {
        const response = await axios.get(`/tasklists/board/${boardId}`);
        return response.data;
}

const moveTaskList = async (taskListId, previousTaskListId = null, nextTaskListId = null, boardId) => {
        const response = await axios.put(`/tasklists/${taskListId}/move`, { 
            previousTaskListId,
            nextTaskListId,
            boardId
        });
        return response.data;
}

export default {
    getTaskLists,
    getAllTaskLists,
    createTaskList,
    updateTaskList,
    deleteTaskList,
    getTaskListByBoardId,
    moveTaskList
}