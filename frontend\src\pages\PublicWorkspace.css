.public-workspace-layout {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.public-workspace-layout .ant-layout-content {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.public-workspace-header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Logo styles - matching Home.css */
.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    color: #1f1f1f;
}

.logo-text {
    background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #fbbc05 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Button styles for login button */
.public-workspace-header .ant-btn-primary {
    background: #667eea;
    border-color: #667eea;
    font-weight: 600;
    height: 40px;
    padding: 0 20px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.public-workspace-header .ant-btn-primary:hover {
    background: #5a67d8 !important;
    border-color: #5a67d8 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.public-workspace-content {
    padding: 24px;
    max-width: none;
    margin: 0 auto;

    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    min-height: calc(100vh - 70px);
}

.loading-container,
.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    text-align: center;
    background: white;
    border-radius: 16px;
    margin: 24px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.workspace-container {
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    background: transparent;
    padding: 24px;
}

.workspace-info-card {
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    background: white;
    width: 100%;
    margin-bottom: 24px;
}

.workspace-header {
    margin-bottom: 16px;
}

.workspace-description-section {
    margin-bottom: 16px;
}

.workspace-description-section .ant-typography {
    margin-bottom: 0;
}

.owner-section,
.boards-section {
    margin: 16px 0;
}

.boards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 12px;
    margin-top: 16px;
}

.board-card {
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
    background: white;
}

.board-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.board-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Board View Styles */
.board-view-wrapper {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    min-height: calc(100vh - 70px);
    width: 100%;
    position: relative;
    margin: 0;
    padding: 24px;
}

.board-view-container {
    display: flex;
    overflow-x: auto;
    padding: 16px 16px 40px 16px; /* Thêm padding bottom để đẩy scroll lên */
    gap: 16px;
    min-height: calc(100vh - 118px); /* Điều chỉnh để tính cả padding */
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    position: relative;
}

.board-view-container::-webkit-scrollbar {
    height: 12px;
}

.board-view-container::-webkit-scrollbar-track {
    background: rgba(241, 245, 249, 0.8);
    border-radius: 6px;
    margin: 0 16px 20px 16px; /* Thêm margin bottom để đẩy track lên */
}

.board-view-container::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 6px;
    border: 2px solid rgba(241, 245, 249, 0.8);
}

.board-view-container::-webkit-scrollbar-thumb:hover {
    background: #5a67d8;
}

/* Scroll indicator */
.board-view-container::before {
    content: "";
    position: sticky;
    left: 0;
    top: 0;
    width: 20px;
    height: 100%;
    background: linear-gradient(
        to right,
        rgba(248, 250, 252, 0.9),
        transparent
    );
    pointer-events: none;
    z-index: 1;
}

.board-view-container::after {
    content: "";
    position: sticky;
    right: 0;
    top: 0;
    width: 20px;
    height: 100%;
    background: linear-gradient(to left, rgba(248, 250, 252, 0.9), transparent);
    pointer-events: none;
    z-index: 1;
}

/* Read-only board column styles */
.board-column.read-only {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    flex-shrink: 0;
    width: 320px;
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 200px);
}

.board-column.read-only .column-header {
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fafafa;
    position: relative;
}

.board-column.read-only .column-title {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 0;
    flex: 1;
}

.board-column.read-only .column-title h3 {
    color: #262626;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.board-column.read-only .column-title .list-color {
    flex-shrink: 0;
}

.board-column.read-only .column-content {
    padding: 16px;
    overflow-y: auto;
    flex: 1;
}

/* Read-only task list styles */
.task-list-container.read-only {
    flex-shrink: 0;
    height: fit-content;
    max-height: calc(100vh - 120px);
}

.task-list-container.read-only .task-list-card {
    background: white;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Task list content with scroll */
.task-list-container.read-only .task-list-content {
    flex: 1;
    overflow-y: auto;
    max-height: calc(100vh - 220px);
    padding-right: 4px;
    scroll-behavior: smooth;
    scrollbar-gutter: stable;
}

/* Custom scrollbar for task list */
.task-list-container.read-only .task-list-content::-webkit-scrollbar {
    width: 6px;
}

.task-list-container.read-only .task-list-content::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.task-list-container.read-only .task-list-content::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.task-list-container.read-only
    .task-list-content::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Tasks container */
.task-list-container.read-only .tasks-container {
    padding-right: 4px;
    position: relative;
}

/* Fade effect at bottom to indicate scrollable content */
.task-list-container.read-only .task-list-content::after {
    content: "";
    position: sticky;
    bottom: 0;
    display: block;
    height: 20px;
    background: linear-gradient(transparent, white);
    pointer-events: none;
    z-index: 1;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.workspace-container {
    animation: fadeInUp 0.6s ease-out;
}

.workspace-info-card {
    animation: fadeInUp 0.8s ease-out;
}

.board-card {
    animation: slideInRight 0.4s ease-out;
}

.board-card:nth-child(2) {
    animation-delay: 0.1s;
}

.board-card:nth-child(3) {
    animation-delay: 0.2s;
}

.board-card:nth-child(4) {
    animation-delay: 0.3s;
}

.task-list-container.read-only {
    animation: slideInRight 0.5s ease-out;
}

.task-list-container.read-only:nth-child(2) {
    animation-delay: 0.1s;
}

.task-list-container.read-only:nth-child(3) {
    animation-delay: 0.2s;
}

.task-list-container.read-only:nth-child(4) {
    animation-delay: 0.3s;
}

/* Button styles for other buttons (not signin) */
.public-workspace-header .ant-btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.public-workspace-header .ant-btn:hover {
    background: #f3f4f6;
    color: #667eea;
    transform: translateY(-1px);
}

/* Alert styles */
.public-workspace-layout .ant-alert {
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.public-workspace-layout .ant-alert-info {
    background: #f8fafc;
    border-color: #667eea;
}

/* Task Detail Modal Notes Scrollbar */
.task-notes-content::-webkit-scrollbar {
    width: 6px;
}

.task-notes-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.task-notes-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.task-notes-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Smooth transitions */
.public-workspace-layout * {
    transition: all 0.2s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .public-workspace-content {
        padding: 16px;
    }

    .workspace-container {
        max-width: 100%;
    }

    .workspace-info-card {
        margin-bottom: 16px;
    }

    .workspace-description-section .ant-typography h4 {
        font-size: 16px;
    }

    .workspace-description-section div {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 8px;
    }

    .boards-grid {
        grid-template-columns: 1fr;
    }

    .header-content {
        padding: 0 16px;
    }

    /* Mobile task list scroll adjustments */
    .task-list-container.read-only {
        max-height: calc(100vh - 140px);
    }

    .task-list-container.read-only .task-list-content {
        max-height: calc(100vh - 240px);
    }
}

@media (max-width: 480px) {
    .public-workspace-content {
        padding: 12px;
    }

    .workspace-container {
        max-width: 100%;
    }

    .workspace-info-card {
        margin: 0 0 12px 0;
        border-radius: 12px;
    }

    /* Small mobile task list scroll adjustments */
    .task-list-container.read-only {
        max-height: calc(100vh - 120px);
    }

    .task-list-container.read-only .task-list-content {
        max-height: calc(100vh - 200px);
    }

    .task-list-container.read-only .task-list-content::-webkit-scrollbar {
        width: 4px;
    }
}

/* Read-only task list styles */
.board-column.read-only {
    flex-shrink: 0;
    width: 320px;
    background: white;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    overflow: hidden;
    max-height: calc(100vh - 200px);
    height: fit-content; /* Chỉ cao vừa đủ với nội dung */
    min-height: auto; /* Không force min-height */
}

.board-column.read-only .column-header {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    padding: 20px 24px;
}

.board-column.read-only .column-title h3 {
    color: #262626;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.board-column.read-only .column-content {
    padding: 16px;
    overflow-y: auto;
    max-height: calc(100vh - 300px);
    min-height: auto; /* Không force min-height */
}

/* Khi có tasks thì mới có min-height */
.board-column.read-only .column-content:has(.tasks-container) {
    min-height: 200px;
}

/* Khi không có tasks (chỉ có Empty) thì giữ ngắn */
.board-column.read-only .column-content:has(.ant-empty) {
    min-height: auto;
    padding: 16px 16px 24px 16px; /* Giảm padding bottom */
}

/* Read-only task card styles */
.board-column.read-only .task-card {
    cursor: default;
    margin-bottom: 8px;
}

.board-column.read-only .task-card:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.board-column.read-only .task-title {
    cursor: default;
}

.board-column.read-only .task-title:hover {
    background: none;
    transform: none;
}

/* Empty state styling cho tasklist không có task */
.board-column.read-only .ant-empty {
    margin: 16px 0 !important; /* Giảm margin của Empty component */
}

.board-column.read-only .ant-empty-description {
    font-size: 13px;
    color: #8c8c8c;
}

.board-column.read-only .ant-empty-image {
    height: 40px !important; /* Giảm kích thước icon */
}
