import axios from '../utils/axiosCustomize';

// <PERSON><PERSON><PERSON> bộ một task list
const syncTaskList = async (taskListId) => {
    try {
        const response = await axios.post(`/sync/tasklist/${taskListId}/sync`);
        return response.data;
    } catch (error) {
        throw error.response?.data || error.message;
    }
};

// Đồng bộ một task
const syncTask = async (taskId) => {
    try {
        const response = await axios.post(`/sync/task/${taskId}/sync`);
        return response.data;
    } catch (error) {
        throw error.response?.data || error.message;
    }
};

// Đồng bộ tất cả tasks trong một task list
const syncAllTasksInList = async (taskListId) => {
    try {
        const response = await axios.post(`/sync/tasklist/${taskListId}/sync-all-tasks`);
        return response.data;
    } catch (error) {
        throw error.response?.data || error.message;
    }
};

// <PERSON><PERSON><PERSON> bộ toàn bộ board
const syncEntireBoard = async (boardId) => {
    try {
        const response = await axios.post(`/async/board/${boardId}/sync`);
        return response.data;
    } catch (error) {
        throw error.response?.data || error.message;
    }
};

export {
    syncTaskList,
    syncTask,
    syncAllTasksInList,
    syncEntireBoard
};