module.exports = (sequelize, DataTypes) => {
    const Task = sequelize.define(
        "Task",
        {
            id: {
                type: DataTypes.STRING(255),
                primaryKey: true,
            },
            tasklistId: {
                type: DataTypes.STRING(255),
                field: "tasklist_id",
            },
            title: {
                type: DataTypes.STRING(255),
                allowNull: false,
            },
            due: {
                type: DataTypes.DATE,
                field: "due",
            },
            position: {
                type: DataTypes.STRING(255),
                field: "position",
            },
            completed: {
                type: DataTypes.DATE,
                field: "completed",
            },
            status: {
                type: DataTypes.STRING(20),
                validate: {
                    isIn: [["needsAction", "completed"]],
                },
            },
            notes: DataTypes.TEXT,
            hidden: {
                type: DataTypes.BOOLEAN,
                defaultValue: false,
                field: "hidden",
            },
            updatedAt: {
                type: DataTypes.DATE,
                field: "updated_at",
            },
            parentTaskId: {
                type: DataTypes.STRING(255),
                field: "parent_task_id",
            },
            priority: {
                type: DataTypes.STRING(255),
                field: "priority",
            },
            googleTaskId: {
                type: DataTypes.STRING(255),
                allowNull: true,
                defaultValue: null,
            },
            createdBy: {
                type: DataTypes.STRING(255),
                field: "created_by",
            },
        },
        {
            tableName: "tasks",
            timestamps: true,
        }
    );

    Task.associate = (models) => {
        Task.belongsTo(models.TaskList, {
            foreignKey: "tasklist_id",
            as: "taskList",
            onDelete: "CASCADE",
        });
        Task.belongsTo(models.User, {
            foreignKey: "created_by",
            as: "creator",
        });
        Task.belongsTo(models.Task, {
            foreignKey: "parent_task_id",
            as: "parentTask",
        });
        Task.hasMany(models.Task, {
            foreignKey: "parent_task_id",
            as: "subtasks",
        });
        Task.hasMany(models.TaskComment, {
            foreignKey: "task_id",
            as: "comments",
            onDelete: "CASCADE",
        });
        Task.hasMany(models.TaskAttachment, {
            foreignKey: "task_id",
            as: "attachments",
            onDelete: "CASCADE",
        });
        Task.hasMany(models.TaskAssignee, {
            foreignKey: "task_id",
            as: "taskAssignees",
            onDelete: "CASCADE",
        });
        Task.belongsToMany(models.User, {
            through: models.TaskAssignee,
            foreignKey: "task_id",
            otherKey: "user_id",
            as: "assignees",
        });
        Task.hasMany(models.TaskLabel, {
            foreignKey: "task_id",
            as: "taskLabels",
            onDelete: "CASCADE",
        });
    };

    return Task;
};
