const express = require('express')
const router = express.Router();
const upload = require('../middleware/uploadMiddleware');
const AttachmentController = require('../controllers/attachmentController');
const { isAuthenticated } = require('../middleware/auth');
const { handleTokenExpiration } = require('../middleware/googleAuth');

router.post('/:taskId', isAuthenticated, handleTokenExpiration, upload.array('files', 10), AttachmentController.uploadAttachment);
router.delete('/:taskId/:fileId', isAuthenticated, handleTokenExpiration, AttachmentController.deleteAttachment);
router.get('/:taskId', isAuthenticated, handleTokenExpiration, AttachmentController.getTaskAttachments);

module.exports = router;